import argparse
import os
import sys
import pandas as pd
from rdkit import Chem
from datetime import datetime


def is_valid_smiles(smiles: str) -> tuple[bool, str]:
    try:
        if pd.isna(smiles):
            return False, "NaN"
        s = str(smiles).strip()
        if not s:
            return False, "Empty"
        mol = Chem.MolFromSmiles(s)
        if mol is None:
            return False, "RDKit MolFromSmiles returned None"
        return True, ""
    except Exception as e:
        return False, f"Exception: {e}"


def scan_csv(csv_path: str, smiles_col: str = "compound_iso_smiles") -> pd.DataFrame:
    try:
        df = pd.read_csv(csv_path)
    except Exception as e:
        return pd.DataFrame([
            {
                "file": csv_path,
                "row_index": None,
                "smiles": None,
                "reason": f"Failed to read CSV: {e}",
            }
        ])

    if smiles_col not in df.columns:
        return pd.DataFrame([
            {
                "file": csv_path,
                "row_index": None,
                "smiles": None,
                "reason": f"Missing column '{smiles_col}'",
            }
        ])

    issues = []
    for idx, smiles in enumerate(df[smiles_col].values):
        ok, reason = is_valid_smiles(smiles)
        if not ok:
            issues.append(
                {
                    "file": csv_path,
                    "row_index": int(idx),
                    "smiles": None if pd.isna(smiles) else str(smiles).strip(),
                    "reason": reason,
                }
            )
    return pd.DataFrame(issues)


def main():
    parser = argparse.ArgumentParser(description="Scan CSVs for invalid SMILES and export a report.")
    parser.add_argument("--data-dir", type=str, default="./data", help="Directory containing CSV files to scan")
    parser.add_argument("--smiles-col", type=str, default="compound_iso_smiles", help="SMILES column name")
    parser.add_argument("--out", type=str, default="./outputs/invalid_smiles_report.csv", help="Output CSV path for the consolidated report")
    args = parser.parse_args()

    data_dir = args.data_dir
    smiles_col = args.smiles_col
    out_path = args.out

    if not os.path.isdir(data_dir):
        print(f"Error: data directory not found: {data_dir}")
        sys.exit(1)

    csv_files = []
    for root, _, files in os.walk(data_dir):
        for f in files:
            if f.lower().endswith(".csv"):
                csv_files.append(os.path.join(root, f))

    if not csv_files:
        print(f"No CSV files found in {data_dir}")
        sys.exit(0)

    print(f"Found {len(csv_files)} CSV files. Scanning...")

    all_issues = []
    for i, csv_path in enumerate(sorted(csv_files)):
        print(f"[{i+1}/{len(csv_files)}] {csv_path}")
        df_issues = scan_csv(csv_path, smiles_col)
        if not df_issues.empty:
            all_issues.append(df_issues)

    os.makedirs(os.path.dirname(out_path), exist_ok=True)

    if not all_issues:
        # write an empty file with header
        pd.DataFrame(columns=["file", "row_index", "smiles", "reason"]).to_csv(out_path, index=False)
        print("All SMILES look valid. Empty report written to:", out_path)
    else:
        report = pd.concat(all_issues, ignore_index=True)
        # add timestamp for traceability
        report.insert(0, "timestamp", datetime.now().isoformat(timespec='seconds'))
        report.to_csv(out_path, index=False)
        print(f"Report written: {out_path}")
        print(f"Total issues: {len(report)}")
        # Also show top reasons summary
        print("Top reasons:")
        print(report["reason"].value_counts().head(10))


if __name__ == "__main__":
    main()
