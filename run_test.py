#!/usr/bin/env python3
"""
运行一个小规模的测试来验证修复
"""
import os
import sys

# 设置环境变量
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'

# 运行主程序，使用小的参数
cmd = [
    sys.executable, 'main.py',
    '--dataset', 'davis',
    '--batch_size', '16',  # 很小的batch size
    '--learning_rate', '0.0001',
    '--max_epochs', '2',   # 只运行2个epoch测试
    '--num_workers', '0',
    '--seed', '0'
]

print("运行测试命令:", ' '.join(cmd))
print("如果出现错误，请查看错误信息...")

import subprocess
try:
    result = subprocess.run(cmd, check=True, capture_output=True, text=True)
    print("测试成功完成！")
    print(result.stdout)
except subprocess.CalledProcessError as e:
    print(f"测试失败，错误码: {e.returncode}")
    print("错误输出:")
    print(e.stderr)
    print("标准输出:")
    print(e.stdout)
except Exception as e:
    print(f"运行测试时出现异常: {e}")
