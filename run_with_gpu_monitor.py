#!/usr/bin/env python3
"""
Script to run the main.py training with GPU monitoring
"""
import subprocess
import threading
import time
import sys
import os
import signal

def monitor_gpu():
    """Monitor GPU usage using nvidia-smi"""
    print("=== GPU监控开始 ===")
    try:
        while True:
            # 获取GPU使用情况
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=index,name,utilization.gpu,memory.used,memory.total,temperature.gpu',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                print(f"\n[{time.strftime('%H:%M:%S')}] GPU状态:")
                for i, line in enumerate(lines):
                    if line.strip():
                        parts = line.split(', ')
                        if len(parts) >= 6:
                            gpu_id, name, util, mem_used, mem_total, temp = parts
                            print(f"  GPU {gpu_id}: {name}")
                            print(f"    利用率: {util}% | 显存: {mem_used}MB/{mem_total}MB | 温度: {temp}°C")
                print("-" * 60)
            else:
                print(f"[{time.strftime('%H:%M:%S')}] 无法获取GPU信息")
            
            time.sleep(10)  # 每10秒监控一次
            
    except KeyboardInterrupt:
        print("\n=== GPU监控结束 ===")
    except Exception as e:
        print(f"GPU监控出错: {e}")

def run_training():
    """运行训练程序"""
    print("=== 开始训练 ===")
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print(f"当前在虚拟环境中: {sys.prefix}")
    else:
        print("警告: 似乎不在虚拟环境中")
    
    # 检查CUDA可用性
    try:
        import torch
        if torch.cuda.is_available():
            print(f"CUDA可用，GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("警告: CUDA不可用，将使用CPU训练")
    except ImportError:
        print("警告: 无法导入torch，请确保已安装PyTorch")
    
    # 构建命令
    cmd = [
        sys.executable, 'main.py',
        '--objective', 'classification',
        '--dataset', 'bindingdb',
        '--batch_size', '64',
        '--max_epochs', '500',
        '--learning_rate', '0.001'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        # 运行训练程序
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 universal_newlines=True, bufsize=1)
        
        # 实时输出训练日志
        for line in process.stdout:
            print(line.rstrip())
            sys.stdout.flush()
        
        process.wait()
        print(f"\n训练程序结束，退出码: {process.returncode}")
        
    except KeyboardInterrupt:
        print("\n用户中断训练")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"训练过程出错: {e}")

def main():
    print("PMMR训练 + GPU监控")
    print("按 Ctrl+C 停止程序")
    print("=" * 60)
    
    # 启动GPU监控线程
    gpu_thread = threading.Thread(target=monitor_gpu, daemon=True)
    gpu_thread.start()
    
    # 等待一下让GPU监控先输出
    time.sleep(2)
    
    # 运行训练
    try:
        run_training()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
