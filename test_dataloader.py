#!/usr/bin/env python3
"""
简单测试DataLoader是否能正常工作
"""

import sys
import os
import torch
from torch.utils.data import DataLoader
from data import CPIDataset

def test_dataloader():
    print("=== 测试DataLoader ===")
    
    # 检查数据文件
    data_path = 'data/'
    davis_csv = data_path + 'davis.csv'
    davis_compound = data_path + 'davis/compound'
    davis_protein = data_path + 'davis/protein'
    
    print(f"检查文件存在性:")
    print(f"  {davis_csv}: {os.path.exists(davis_csv)}")
    print(f"  {davis_compound}: {os.path.exists(davis_compound)}")
    print(f"  {davis_protein}: {os.path.exists(davis_protein)}")
    
    if not all([os.path.exists(davis_csv), os.path.exists(davis_compound), os.path.exists(davis_protein)]):
        print("错误: 数据文件不完整")
        return False
    
    try:
        print("\n1. 创建数据集...")
        dataset = CPIDataset(davis_csv, davis_compound, davis_protein)
        print(f"   数据集大小: {len(dataset)}")
        
        print("\n2. 测试单个样本...")
        sample = dataset[0]
        print(f"   样本键: {sample.keys()}")
        for key, value in sample.items():
            if hasattr(value, 'shape'):
                print(f"   {key}: {value.shape}")
            else:
                print(f"   {key}: {type(value)} = {value}")
        
        print("\n3. 测试DataLoader (num_workers=0)...")
        loader = DataLoader(
            dataset,
            batch_size=4,
            collate_fn=dataset.collate_fn,
            shuffle=False,
            num_workers=0,
            drop_last=False,
        )
        
        batch = next(iter(loader))
        print(f"   批次大小: {len(batch['LABEL'])}")
        for key, value in batch.items():
            if hasattr(value, 'shape'):
                print(f"   {key}: {value.shape}")
        
        print("\n4. 测试DataLoader (num_workers=2)...")
        loader_mp = DataLoader(
            dataset,
            batch_size=4,
            collate_fn=dataset.collate_fn,
            shuffle=False,
            num_workers=2,
            drop_last=False,
        )
        
        batch_mp = next(iter(loader_mp))
        print(f"   多进程批次大小: {len(batch_mp['LABEL'])}")
        print("   多进程测试成功!")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_dataloader()
    if success:
        print("\n✓ 所有测试通过!")
    else:
        print("\n✗ 测试失败!")
        sys.exit(1)
