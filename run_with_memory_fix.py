#!/usr/bin/env python3
"""
启动脚本，用于解决CUDA内存和多进程问题
"""
import os
import sys
import subprocess

def setup_environment():
    """设置环境变量以解决内存和多进程问题"""
    
    # 设置PyTorch CUDA内存分配策略
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    
    # 设置CUDA内存分片策略
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    
    # 在Windows上禁用多进程共享内存
    if os.name == 'nt':  # Windows
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 只使用一个GPU
        print("Windows环境检测到，已设置单GPU模式")
    
    # 设置OMP线程数以避免过度并行化
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    
    print("环境变量设置完成:")
    print(f"PYTORCH_CUDA_ALLOC_CONF: {os.environ.get('PYTORCH_CUDA_ALLOC_CONF')}")
    print(f"CUDA_LAUNCH_BLOCKING: {os.environ.get('CUDA_LAUNCH_BLOCKING')}")
    print(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")

def main():
    """主函数"""
    setup_environment()
    
    # 获取命令行参数
    args = sys.argv[1:] if len(sys.argv) > 1 else []
    
    # 默认参数，使用较小的batch size
    default_args = [
        '--dataset', 'davis',
        '--batch_size', '32',  # 减小batch size
        '--learning_rate', '0.0001',
        '--max_epochs', '100',
        '--num_workers', '0',  # 禁用多进程
        '--seed', '0'
    ]
    
    # 如果没有提供参数，使用默认参数
    if not args:
        args = default_args
        print("使用默认参数:", ' '.join(args))
    
    # 运行主程序
    cmd = [sys.executable, 'main.py'] + args
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"程序执行失败，错误码: {e.returncode}")
        sys.exit(e.returncode)
    except KeyboardInterrupt:
        print("程序被用户中断")
        sys.exit(1)

if __name__ == '__main__':
    main()
