#!/usr/bin/env python3
"""
测试网络连接和模型下载
"""

import os
import sys

def test_imports():
    """测试必要的库是否可以导入"""
    print("测试库导入...")
    try:
        import transformers
        print(f"✓ transformers版本: {transformers.__version__}")
    except ImportError as e:
        print(f"✗ transformers导入失败: {e}")
        return False
    
    try:
        import torch
        print(f"✓ torch版本: {torch.__version__}")
    except ImportError as e:
        print(f"✗ torch导入失败: {e}")
        return False
    
    return True

def test_network():
    """测试网络连接"""
    print("\n测试网络连接...")
    try:
        import requests
        response = requests.get("https://huggingface.co", timeout=10)
        if response.status_code == 200:
            print("✓ 可以访问HuggingFace")
            return True
        else:
            print(f"✗ HuggingFace返回状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 网络连接失败: {e}")
        return False

def test_model_download():
    """测试模型下载"""
    print("\n测试模型下载...")
    try:
        from transformers import AutoTokenizer
        
        # 尝试下载tokenizer（比较小）
        print("尝试下载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained("DeepChem/ChemBERTa-77M-MLM")
        print("✓ tokenizer下载成功")
        
        # 保存到本地
        save_path = "./models/ChemBERTa-77M-MLM"
        os.makedirs(save_path, exist_ok=True)
        tokenizer.save_pretrained(save_path)
        print(f"✓ tokenizer保存到: {save_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型下载失败: {e}")
        return False

def main():
    print("ChemBERTa模型下载测试")
    print("=" * 50)
    
    # 测试库导入
    if not test_imports():
        print("\n请先安装必要的库:")
        print("pip install transformers torch")
        sys.exit(1)
    
    # 测试网络连接
    if not test_network():
        print("\n网络连接有问题，可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 设置代理:")
        print("   set HTTP_PROXY=http://your-proxy:port")
        print("   set HTTPS_PROXY=https://your-proxy:port")
        print("3. 使用VPN")
        sys.exit(1)
    
    # 测试模型下载
    if test_model_download():
        print("\n✓ 测试成功！可以继续下载完整模型")
        
        # 下载完整模型
        try:
            print("\n开始下载完整模型...")
            from transformers import RobertaModel
            model = RobertaModel.from_pretrained("DeepChem/ChemBERTa-77M-MLM")
            save_path = "./models/ChemBERTa-77M-MLM"
            model.save_pretrained(save_path)
            print(f"✓ 完整模型保存到: {save_path}")
            
            print(f"\n成功！现在可以使用以下命令运行预处理:")
            print(f"python compound_pretrain.py --model_path {save_path}")
            
        except Exception as e:
            print(f"✗ 完整模型下载失败: {e}")
            sys.exit(1)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
