
import os.path
import numpy as np
import pandas as pd
import torch
import esm
import argparse
import os



def get_pretrained_embedding(s):
    # Check if GPU is available
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Initialize model and batch converter
    model, alphabet = esm.pretrained.esm2_t12_35M_UR50D()
    batch_converter = alphabet.get_batch_converter()
    model.eval()

    # Move model to GPU
    model = model.to(device)

    batch_labels, batch_strs, batch_tokens = batch_converter([("protein", s)])
    batch_lens = (batch_tokens != alphabet.padding_idx).sum(1)

    # Move tokens to GPU
    batch_tokens = batch_tokens.to(device)

    # Extract per-residue representations (on GPU) - 禁用contact计算以节省内存
    with torch.no_grad():
        results = model(batch_tokens, repr_layers=[12], return_contacts=False)
    token_representations = results["representations"][12]

    # Generate per-sequence representations via averaging
    # NOTE: token 0 is always a beginning-of-sequence token, so the first residue is token 1.
    sequence_representations = []
    for i, tokens_len in enumerate(batch_lens):
        # Move result back to CPU for processing and saving
        sequence_representations.append(token_representations[i, 1: tokens_len - 1].cpu())

    # 清理GPU内存
    del model, batch_tokens, token_representations, results
    torch.cuda.empty_cache()

    return sequence_representations[0]


def generate_feature(args):

    data_path = args.root_data_path
    dataset = args.dataset
    output_data_path = data_path + '/' + dataset + '/protein/'


    opts = ['train', 'test']

    for o in opts:
        # 创建每个子目录 (train/test)
        sub_output_path = f'{output_data_path}/{o}/'
        if not os.path.exists(sub_output_path):
            # 如果文件夹不存在，则创建
            os.makedirs(sub_output_path)
            print(f"{sub_output_path} created")
        else:
            print(f"{sub_output_path} exists")

        count = 0
        id_list = []
        raw_data = pd.read_csv(f'{data_path}/{dataset}_{o}.csv')
        sequence_values = raw_data['target_sequence'].values
        print(f"Processing {o} data: {len(sequence_values)} sequences")

        for i, s in enumerate(sequence_values):
            id = str(raw_data['target_id'][i])
            if id in id_list:
                print(f"Skipping duplicate ID: {id}")
                continue
            if os.path.isfile(f'{output_data_path}/{o}/' + id + '.npy'):
                print(f"File already exists, skipping: {id}")
                continue

            print(f"Processing sequence {i+1}/{len(sequence_values)}, ID: {id}")
            seq_emb = get_pretrained_embedding(s.upper())
            print(f"Embedding shape: {seq_emb.shape}")
            np.save(f'{output_data_path}/{o}/' + id, seq_emb)
            print(f"Saved: {id}")
            id_list.append(id)
            count += 1

        print(f"Processed {count} new sequences for {o}")
    print(f"Total new sequences processed: {count}")



def parse_args():
    parser = argparse.ArgumentParser()

    parser.add_argument('--root_data_path', type=str, default='../../data', help='Raw Data Path')
    parser.add_argument('--dataset', type=str, default='bindingdb', help='Datasets')
    return parser.parse_args()

if __name__ == '__main__':
    params = parse_args()
    print(f"Parameters: {params}")

    # 只处理测试数据
    data_path = params.root_data_path
    dataset = params.dataset
    output_data_path = data_path + '/' + dataset + '/protein/'

    # 只处理test数据
    o = 'test'
    sub_output_path = f'{output_data_path}/{o}/'
    if not os.path.exists(sub_output_path):
        os.makedirs(sub_output_path)
        print(f"{sub_output_path} created")
    else:
        print(f"{sub_output_path} exists")

    count = 0
    id_list = []
    raw_data = pd.read_csv(f'{data_path}/{dataset}_{o}.csv')
    sequence_values = raw_data['target_sequence'].values
    print(f"Processing {o} data: {len(sequence_values)} sequences")

    for i, s in enumerate(sequence_values):
        id = str(raw_data['target_id'][i])
        if id in id_list:
            print(f"Skipping duplicate ID: {id}")
            continue
        if os.path.isfile(f'{output_data_path}/{o}/' + id + '.npy'):
            print(f"File already exists, skipping: {id}")
            continue

        print(f"Processing sequence {i+1}/{len(sequence_values)}, ID: {id}")
        seq_emb = get_pretrained_embedding(s.upper())
        print(f"Embedding shape: {seq_emb.shape}")
        np.save(f'{output_data_path}/{o}/' + id, seq_emb)
        print(f"Saved: {id}")
        id_list.append(id)
        count += 1

        # 每处理10个序列就打印一次进度
        if count % 10 == 0:
            print(f"Processed {count} sequences so far...")

    print(f"Processed {count} new sequences for {o}")
    print("Finished!")






