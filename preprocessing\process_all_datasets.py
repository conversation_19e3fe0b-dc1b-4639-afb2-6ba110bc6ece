#!/usr/bin/env python3
"""
批量处理所有数据集的化合物特征提取
"""

import subprocess
import sys
import os
import time

def run_preprocessing(dataset, model_path="./models/ChemBERTa-77M-MLM"):
    """运行单个数据集的预处理"""
    print(f"\n{'='*50}")
    print(f"开始处理数据集: {dataset}")
    print(f"{'='*50}")
    
    cmd = [
        "python", "compound_pretrain.py",
        "--model_path", model_path,
        "--dataset", dataset
    ]
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            end_time = time.time()
            duration = end_time - start_time
            print(f"✓ {dataset} 处理成功！耗时: {duration:.2f}秒")
            print(f"输出: {result.stdout}")
            return True
        else:
            print(f"✗ {dataset} 处理失败！")
            print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ {dataset} 处理超时（30分钟）")
        return False
    except Exception as e:
        print(f"✗ {dataset} 处理出错: {e}")
        return False

def check_model_exists(model_path):
    """检查模型是否存在"""
    if not os.path.exists(model_path):
        print(f"错误: 模型路径不存在: {model_path}")
        print("请先运行: python test_download.py")
        return False
    
    required_files = ["config.json", "model.safetensors", "tokenizer.json"]
    for file in required_files:
        if not os.path.exists(os.path.join(model_path, file)):
            print(f"错误: 缺少模型文件: {file}")
            return False
    
    return True

def main():
    print("批量处理所有数据集的化合物特征提取")
    print("=" * 60)
    
    model_path = "./models/ChemBERTa-77M-MLM"
    
    # 检查模型是否存在
    if not check_model_exists(model_path):
        sys.exit(1)
    
    # 定义要处理的数据集
    datasets = ["davis", "bindingdb", "pdb", "tdc_dg"]
    
    # 检查数据文件是否存在
    data_root = "../data"
    missing_datasets = []
    
    for dataset in datasets:
        if dataset == "davis":
            # davis使用单个文件
            if not os.path.exists(f"{data_root}/{dataset}.csv"):
                missing_datasets.append(f"{dataset}.csv")
        else:
            # 其他数据集使用train/test文件
            for split in ["train", "test"]:
                if not os.path.exists(f"{data_root}/{dataset}_{split}.csv"):
                    missing_datasets.append(f"{dataset}_{split}.csv")
    
    if missing_datasets:
        print("错误: 缺少以下数据文件:")
        for file in missing_datasets:
            print(f"  - {file}")
        sys.exit(1)
    
    # 处理每个数据集
    results = {}
    total_start_time = time.time()
    
    for dataset in datasets:
        success = run_preprocessing(dataset, model_path)
        results[dataset] = success
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 打印总结
    print(f"\n{'='*60}")
    print("处理总结")
    print(f"{'='*60}")
    print(f"总耗时: {total_duration:.2f}秒 ({total_duration/60:.2f}分钟)")
    print()
    
    success_count = 0
    for dataset, success in results.items():
        status = "✓ 成功" if success else "✗ 失败"
        print(f"{dataset:12} : {status}")
        if success:
            success_count += 1
    
    print(f"\n成功: {success_count}/{len(datasets)} 个数据集")
    
    if success_count == len(datasets):
        print("\n🎉 所有数据集处理完成！现在可以运行主训练脚本了。")
    else:
        print(f"\n⚠️  有 {len(datasets) - success_count} 个数据集处理失败，请检查错误信息。")

if __name__ == "__main__":
    main()
