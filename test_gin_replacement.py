#!/usr/bin/env python3
"""
测试GIN替换GCN后的模型功能
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.gnn import MultiGCN, MultiGIN, GINLayer, GIN
from models.core import PMMRNet

def test_gin_layer():
    """测试GINLayer的基本功能"""
    print("测试GINLayer...")
    
    # 创建测试数据
    batch_size = 2
    num_nodes = 5
    input_dim = 10
    output_dim = 8
    
    # 创建GINLayer
    gin_layer = GINLayer(input_dim, output_dim, eps=0.1, train_eps=True)
    
    # 测试批处理模式
    x_batch = torch.randn(batch_size, num_nodes, input_dim)
    adj_batch = torch.randn(batch_size, num_nodes, num_nodes)
    adj_batch = torch.softmax(adj_batch, dim=-1)  # 归一化邻接矩阵
    
    output_batch = gin_layer(adj_batch, x_batch)
    print(f"  批处理输入形状: {x_batch.shape}")
    print(f"  批处理输出形状: {output_batch.shape}")
    assert output_batch.shape == (batch_size, num_nodes, output_dim)
    
    # 测试单图模式
    x_single = torch.randn(num_nodes, input_dim)
    adj_single = torch.randn(num_nodes, num_nodes)
    adj_single = torch.softmax(adj_single, dim=-1)
    
    output_single = gin_layer(adj_single, x_single)
    print(f"  单图输入形状: {x_single.shape}")
    print(f"  单图输出形状: {output_single.shape}")
    assert output_single.shape == (num_nodes, output_dim)
    
    print("  ✓ GINLayer测试通过")

def test_multi_gin_vs_multi_gcn():
    """测试MultiGIN与MultiGCN的接口兼容性"""
    print("\n测试MultiGIN与MultiGCN接口兼容性...")
    
    batch_size = 3
    num_nodes = 8
    in_dim = 78  # 与实际模型中的compound_structure_dim一致
    out_dim = 64  # 与实际模型中的gnn_dim类似
    
    # 创建测试数据
    inputs = torch.randn(batch_size, num_nodes, in_dim)
    adj = torch.randn(batch_size, num_nodes, num_nodes)
    adj = torch.softmax(adj, dim=-1)
    
    # 测试MultiGCN
    multi_gcn = MultiGCN(in_dim, out_dim)
    gcn_output = multi_gcn(inputs, adj)
    print(f"  MultiGCN输出形状: {gcn_output.shape}")
    
    # 测试MultiGIN
    multi_gin = MultiGIN(in_dim, out_dim)
    gin_output = multi_gin(inputs, adj)
    print(f"  MultiGIN输出形状: {gin_output.shape}")
    
    # 验证输出形状一致
    assert gcn_output.shape == gin_output.shape
    print(f"  ✓ 输出形状一致: {gin_output.shape}")
    
    # 验证参数数量
    gcn_params = sum(p.numel() for p in multi_gcn.parameters())
    gin_params = sum(p.numel() for p in multi_gin.parameters())
    print(f"  MultiGCN参数数量: {gcn_params}")
    print(f"  MultiGIN参数数量: {gin_params}")
    
    print("  ✓ MultiGIN接口兼容性测试通过")

def test_model_forward():
    """测试完整模型的前向传播"""
    print("\n测试完整模型前向传播...")
    
    # 创建模拟的args对象
    class Args:
        def __init__(self):
            self.compound_gnn_dim = 78
            self.dropout = 0.2
            self.decoder_dim = 128
            self.decoder_heads = 8
            self.compound_text_dim = 128
            self.compound_structure_dim = 78
            self.protein_dim = 128
            self.linear_heads = 10
            self.linear_hidden_dim = 32
            self.pf_dim = 1024
            self.encoder_heads = 8
            self.encoder_layers = 2
            self.protein_pretrained_dim = 1280
            self.compound_pretrained_dim = 384
            self.objective = 'regression'
    
    args = Args()
    
    try:
        # 检查GPU可用性
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"  使用设备: {device}")

        # 创建模型并移动到GPU
        model = PMMRNet(args)
        if torch.cuda.is_available():
            model = model.cuda()
        print(f"  ✓ 模型创建成功")

        # 创建模拟数据
        batch_size = 2
        max_nodes = 50
        max_smiles_len = 100
        max_protein_len = 500

        data = {
            'COMPOUND_NODE_FEAT': torch.randn(batch_size, max_nodes, args.compound_structure_dim),
            'COMPOUND_ADJ': torch.softmax(torch.randn(batch_size, max_nodes, max_nodes), dim=-1),
            'COMPOUND_EMBEDDING': torch.randn(batch_size, max_smiles_len, args.compound_pretrained_dim),
            'PROTEIN_EMBEDDING': torch.randn(batch_size, max_protein_len, args.protein_pretrained_dim),
            'COMPOUND_NODE_NUM': torch.tensor([max_nodes] * batch_size),
            'COMPOUND_SMILES_LENGTH': torch.tensor([max_smiles_len] * batch_size),
            'PROTEIN_NODE_NUM': torch.tensor([max_protein_len] * batch_size),
        }
        
        # 前向传播
        with torch.no_grad():
            output = model(data)
        
        print(f"  ✓ 前向传播成功，输出形状: {output.shape}")
        
        # 验证输出形状
        if args.objective == 'regression':
            expected_shape = (batch_size, 1)
        else:
            expected_shape = (batch_size, 2)
        
        assert output.shape == expected_shape
        print(f"  ✓ 输出形状正确: {output.shape}")
        
    except Exception as e:
        print(f"  ✗ 模型测试失败: {e}")
        raise

def main():
    print("开始测试GIN替换GCN的功能...")
    print("=" * 50)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # 测试GINLayer
        test_gin_layer()
        
        # 测试MultiGIN与MultiGCN的兼容性
        test_multi_gin_vs_multi_gcn()
        
        # 测试完整模型
        test_model_forward()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！GIN替换GCN成功！")
        print("模型现在使用Graph Isomorphism Network (GIN)而不是GCN")
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
