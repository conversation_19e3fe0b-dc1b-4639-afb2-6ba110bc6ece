import torch
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from models.gnn import MultiGIN, MultiGCN
    print("✓ 成功导入MultiGIN和MultiGCN")
    
    # 测试基本功能
    batch_size = 2
    num_nodes = 5
    in_dim = 10
    out_dim = 8
    
    inputs = torch.randn(batch_size, num_nodes, in_dim)
    adj = torch.softmax(torch.randn(batch_size, num_nodes, num_nodes), dim=-1)
    
    # 测试MultiGCN
    multi_gcn = MultiGCN(in_dim, out_dim)
    gcn_output = multi_gcn(inputs, adj)
    print(f"✓ MultiGCN输出形状: {gcn_output.shape}")
    
    # 测试MultiGIN
    multi_gin = MultiGIN(in_dim, out_dim)
    gin_output = multi_gin(inputs, adj)
    print(f"✓ MultiGIN输出形状: {gin_output.shape}")
    
    print("✓ 基本功能测试通过")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
