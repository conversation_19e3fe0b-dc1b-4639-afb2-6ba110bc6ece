#!/usr/bin/env python3
"""
检查预处理状态
"""

import os
import pickle

def check_dataset_status(dataset):
    """检查单个数据集的处理状态"""
    compound_dir = f"../data/{dataset}/compound"
    mol_dict_path = f"{compound_dir}/mol_dict.pkl"
    
    if not os.path.exists(compound_dir):
        return "未开始", 0
    
    if not os.path.exists(mol_dict_path):
        return "处理中", 0
    
    try:
        with open(mol_dict_path, 'rb') as f:
            mol_dict = pickle.load(f)
        return "已完成", len(mol_dict)
    except Exception as e:
        return f"错误: {e}", 0

def main():
    print("预处理状态检查")
    print("=" * 40)
    
    datasets = ["davis", "bindingdb", "pdb", "tdc_dg"]
    
    for dataset in datasets:
        status, count = check_dataset_status(dataset)
        print(f"{dataset:12} : {status:10} ({count} 个化合物)")
    
    print("\n模型状态:")
    model_path = "./models/ChemBERTa-77M-MLM"
    if os.path.exists(model_path):
        print(f"✓ 模型已下载: {model_path}")
    else:
        print(f"✗ 模型未下载: {model_path}")

if __name__ == "__main__":
    main()
