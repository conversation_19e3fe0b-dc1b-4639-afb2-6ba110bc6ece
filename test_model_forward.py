#!/usr/bin/env python3
"""
测试模型forward函数是否正常工作 - 包含CrossAttention测试
"""
import torch
import argparse
from models.core import PMMRNet, CrossAttention

def test_cross_attention():
    print("测试CrossAttention基本功能...")

    try:
        # 测试CrossAttention
        cross_attn = CrossAttention(128, 32, 10)

        # 创建测试数据
        batch_size = 2
        seq_len = 10
        x = torch.randn(batch_size, seq_len, 128)
        masks = torch.ones(batch_size, cross_attn.heads, seq_len)

        # 前向传播
        output = cross_attn(x, masks)
        print(f"✓ CrossAttention测试成功，输出形状: {output.shape}")
        print(f"✓ 实际使用的heads数量: {cross_attn.heads}")

    except Exception as e:
        print(f"✗ CrossAttention测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_model():
    print("测试模型forward函数...")

    # 创建模型参数
    args = argparse.Namespace()
    args.decoder_layers = 3
    args.linear_heads = 10
    args.linear_hidden_dim = 32
    args.decoder_heads = 4
    args.encoder_heads = 4
    args.gnn_layers = 3
    args.encoder_layers = 1
    args.decoder_nums = 1
    args.decoder_dim = 128
    args.compound_gnn_dim = 78
    args.pf_dim = 1024
    args.dropout = 0.2
    args.protein_dim = 128
    args.compound_structure_dim = 78
    args.compound_text_dim = 128
    args.compound_pretrained_dim = 384
    args.protein_pretrained_dim = 480
    args.objective = 'regression'
    
    try:
        # 创建模型
        model = PMMRNet(args)
        if torch.cuda.is_available():
            model = model.cuda()
        print("✓ 模型创建成功")
        
        # 创建测试数据
        batch_size = 2
        max_nodes = 20
        max_smiles_len = 50
        max_protein_len = 100
        
        data = {
            'COMPOUND_NODE_FEAT': torch.randn(batch_size, max_nodes, args.compound_structure_dim),
            'COMPOUND_ADJ': torch.rand(batch_size, max_nodes, max_nodes),
            'COMPOUND_EMBEDDING': torch.randn(batch_size, max_smiles_len, args.compound_pretrained_dim),
            'PROTEIN_EMBEDDING': torch.randn(batch_size, max_protein_len, args.protein_pretrained_dim),
            'COMPOUND_NODE_NUM': torch.tensor([max_nodes, max_nodes-5]),
            'COMPOUND_SMILES_LENGTH': torch.tensor([max_smiles_len, max_smiles_len-10]),
            'PROTEIN_NODE_NUM': torch.tensor([max_protein_len, max_protein_len-20]),
            'LABEL': torch.randn(batch_size, 1)
        }
        
        if torch.cuda.is_available():
            for key in data:
                if key != 'LABEL':  # LABEL在训练时才移到GPU
                    data[key] = data[key].cuda()
        
        print("✓ 测试数据创建成功")
        
        # 测试forward
        model.eval()
        with torch.no_grad():
            output = model(data)
            print(f"✓ Forward测试成功，输出形状: {output.shape}")
            
        print("\n模型测试通过！可以运行训练了。")
        return True
        
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始测试CrossAttention替换...")
    test_cross_attention()
    print("\n" + "="*50)
    test_model()
    print("\n测试完成！")
