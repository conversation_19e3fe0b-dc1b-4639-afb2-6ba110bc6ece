#!/usr/bin/env python3
"""
测试内存修复是否有效的脚本
"""
import os
import sys
import torch
import platform
from data_loader_fix import setup_multiprocessing, create_safe_dataloader

def test_cuda_memory():
    """测试CUDA内存设置"""
    print("=== CUDA内存测试 ===")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过CUDA测试")
        return False
    
    print(f"CUDA设备数量: {torch.cuda.device_count()}")
    print(f"当前CUDA设备: {torch.cuda.current_device()}")
    
    # 测试内存分配
    try:
        device = torch.cuda.current_device()
        print(f"GPU {device} 内存信息:")
        print(f"  总内存: {torch.cuda.get_device_properties(device).total_memory / 1024**3:.2f} GB")
        print(f"  已分配: {torch.cuda.memory_allocated(device) / 1024**3:.2f} GB")
        print(f"  已缓存: {torch.cuda.memory_reserved(device) / 1024**3:.2f} GB")
        
        # 测试小批量内存分配
        test_tensor = torch.randn(100, 100).cuda()
        print("小批量内存分配测试: 通过")
        del test_tensor
        torch.cuda.empty_cache()
        
        return True
    except Exception as e:
        print(f"CUDA内存测试失败: {e}")
        return False

def test_multiprocessing_setup():
    """测试多进程设置"""
    print("\n=== 多进程设置测试 ===")
    
    try:
        setup_multiprocessing()
        print("多进程设置: 完成")
        
        # 检查当前平台
        system = platform.system()
        print(f"操作系统: {system}")
        
        if system == "Windows":
            print("Windows环境检测到，将使用单进程模式")
        else:
            print("非Windows环境，可以使用多进程模式")
        
        return True
    except Exception as e:
        print(f"多进程设置失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量设置"""
    print("\n=== 环境变量测试 ===")
    
    # 设置关键环境变量
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    
    # 检查环境变量
    env_vars = [
        'PYTORCH_CUDA_ALLOC_CONF',
        'CUDA_LAUNCH_BLOCKING', 
        'OMP_NUM_THREADS',
        'MKL_NUM_THREADS'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"{var}: {value}")
    
    return True

def test_dataloader_creation():
    """测试DataLoader创建"""
    print("\n=== DataLoader创建测试 ===")
    
    try:
        # 创建一个简单的测试数据集
        class TestDataset(torch.utils.data.Dataset):
            def __init__(self, size=100):
                self.size = size
            
            def __len__(self):
                return self.size
            
            def __getitem__(self, idx):
                return {
                    'data': torch.randn(10),
                    'label': torch.randint(0, 2, (1,))
                }
        
        dataset = TestDataset(50)
        
        # 使用安全的DataLoader创建函数
        dataloader = create_safe_dataloader(
            dataset,
            batch_size=8,
            shuffle=True
        )
        
        print(f"DataLoader创建成功")
        print(f"  批次大小: {dataloader.batch_size}")
        print(f"  工作进程数: {dataloader.num_workers}")
        print(f"  Pin memory: {dataloader.pin_memory}")
        
        # 测试数据加载
        for i, batch in enumerate(dataloader):
            if i >= 2:  # 只测试前两个批次
                break
            print(f"  批次 {i+1}: 数据形状 {batch['data'].shape}, 标签形状 {batch['label'].shape}")
        
        print("DataLoader测试: 通过")
        return True
        
    except Exception as e:
        print(f"DataLoader测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始内存修复测试...\n")
    
    tests = [
        ("环境变量设置", test_environment_variables),
        ("多进程设置", test_multiprocessing_setup),
        ("CUDA内存", test_cuda_memory),
        ("DataLoader创建", test_dataloader_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    all_passed = True
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("="*50)
    if all_passed:
        print("所有测试通过！可以尝试运行主程序。")
        print("建议使用: python run_with_memory_fix.py")
    else:
        print("部分测试失败，请检查环境配置。")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
