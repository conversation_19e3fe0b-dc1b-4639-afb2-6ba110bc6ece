#!/usr/bin/env python3
"""
最终测试修复
"""
import torch
import argparse
from models.core import PMMRNet

def test_model():
    print("测试修复后的模型...")
    
    # 创建模型参数
    args = argparse.Namespace()
    args.decoder_layers = 3
    args.linear_heads = 10
    args.linear_hidden_dim = 32
    args.decoder_heads = 4
    args.encoder_heads = 4
    args.gnn_layers = 3
    args.encoder_layers = 1
    args.decoder_nums = 1
    args.decoder_dim = 128
    args.compound_gnn_dim = 78
    args.pf_dim = 1024
    args.dropout = 0.2
    args.protein_dim = 128
    args.compound_structure_dim = 78
    args.compound_text_dim = 128
    args.compound_pretrained_dim = 384
    args.protein_pretrained_dim = 480
    args.objective = 'regression'
    
    try:
        # 创建模型
        model = PMMRNet(args)
        model.eval()
        print("✓ 模型创建成功")
        
        print(f"drug_attn heads: {model.drug_attn.heads}")
        print(f"target_attn heads: {model.target_attn.heads}")
        print(f"inter_attn_one heads: {model.inter_attn_one.heads}")
        
        # 创建测试数据
        batch_size = 2
        compound_nodes = 10
        protein_length = 20
        smiles_length = 15
        
        data = {
            'COMPOUND_NODE_FEAT': torch.randn(batch_size, compound_nodes, args.compound_structure_dim),
            'COMPOUND_ADJ': torch.rand(batch_size, compound_nodes, compound_nodes),
            'COMPOUND_EMBEDDING': torch.randn(batch_size, smiles_length, args.compound_pretrained_dim),
            'PROTEIN_EMBEDDING': torch.randn(batch_size, protein_length, args.protein_pretrained_dim),
            'COMPOUND_NODE_NUM': torch.tensor([compound_nodes, compound_nodes-2]),
            'COMPOUND_SMILES_LENGTH': torch.tensor([smiles_length, smiles_length-3]),
            'PROTEIN_NODE_NUM': torch.tensor([protein_length, protein_length-5]),
            'LABEL': torch.randn(batch_size, 1)
        }
        
        print("✓ 测试数据创建成功")
        
        # 前向传播
        with torch.no_grad():
            output = model(data)
            print(f"✓ 前向传播成功，输出形状: {output.shape}")
            
        print("🎉 所有测试通过！修复成功！")
        return True
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_model()
    if success:
        print("\n✅ CrossAttention替换完成且功能正常！")
    else:
        print("\n❌ 仍有问题需要修复")
    exit(0 if success else 1)
