#!/usr/bin/env python3
"""
测试mask维度修复
"""
import torch
import argparse
from models.core import CrossAttention, PMMRNet

def test_mask_dimension_fix():
    print("测试mask维度修复...")
    
    # 测试不同的mask维度情况
    input_dim = 128
    hidden_dim = 32
    original_heads = 10
    
    # 创建CrossAttention（会自动调整heads为8）
    cross_attn = CrossAttention(input_dim, hidden_dim, original_heads)
    print(f"原始heads: {original_heads}, 调整后heads: {cross_attn.heads}")
    
    # 创建测试数据
    batch_size = 2
    seq_len = 10
    x = torch.randn(batch_size, seq_len, input_dim)
    
    # 创建原始维度的mask（使用原始heads数量）
    masks = torch.ones(batch_size, original_heads, seq_len)
    
    try:
        # 前向传播
        output = cross_attn(x, masks)
        print(f"✓ 测试成功，输出形状: {output.shape}")
        return True
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_forward():
    print("\n测试完整模型前向传播...")
    
    # 创建模型参数
    args = argparse.Namespace()
    args.decoder_layers = 3
    args.linear_heads = 10
    args.linear_hidden_dim = 32
    args.decoder_heads = 4
    args.encoder_heads = 4
    args.gnn_layers = 3
    args.encoder_layers = 1
    args.decoder_nums = 1
    args.decoder_dim = 128
    args.compound_gnn_dim = 78
    args.pf_dim = 1024
    args.dropout = 0.2
    args.protein_dim = 128
    args.compound_structure_dim = 78
    args.compound_text_dim = 128
    args.compound_pretrained_dim = 384
    args.protein_pretrained_dim = 480
    args.objective = 'regression'
    
    try:
        # 创建模型
        model = PMMRNet(args)
        model.eval()
        print("✓ 模型创建成功")
        
        # 创建测试数据
        batch_size = 2
        compound_nodes = 10
        protein_length = 20
        smiles_length = 15
        
        data = {
            'COMPOUND_NODE_FEAT': torch.randn(batch_size, compound_nodes, args.compound_structure_dim),
            'COMPOUND_ADJ': torch.rand(batch_size, compound_nodes, compound_nodes),
            'COMPOUND_EMBEDDING': torch.randn(batch_size, smiles_length, args.compound_pretrained_dim),
            'PROTEIN_EMBEDDING': torch.randn(batch_size, protein_length, args.protein_pretrained_dim),
            'COMPOUND_NODE_NUM': torch.tensor([compound_nodes, compound_nodes-2]),
            'COMPOUND_SMILES_LENGTH': torch.tensor([smiles_length, smiles_length-3]),
            'PROTEIN_NODE_NUM': torch.tensor([protein_length, protein_length-5]),
            'LABEL': torch.randn(batch_size, 1)
        }
        
        # 前向传播
        with torch.no_grad():
            output = model(data)
            print(f"✓ 前向传播成功，输出形状: {output.shape}")
            return True
            
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("开始测试mask维度修复...")
    
    success1 = test_mask_dimension_fix()
    success2 = test_model_forward()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！mask维度问题已修复！")
        return True
    else:
        print("\n❌ 测试失败，需要进一步修复")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
