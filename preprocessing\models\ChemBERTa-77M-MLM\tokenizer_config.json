{"add_prefix_space": false, "added_tokens_decoder": {"0": {"content": "[PAD]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "11": {"content": "[UNK]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "12": {"content": "[CLS]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "13": {"content": "[SEP]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "14": {"content": "[MASK]", "lstrip": true, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "591": {"content": "<s>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "592": {"content": "</s>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}}, "bos_token": "<s>", "clean_up_tokenization_spaces": false, "cls_token": "[CLS]", "eos_token": "</s>", "errors": "replace", "extra_special_tokens": {}, "full_tokenizer_file": null, "mask_token": "[MASK]", "max_len": 512, "model_max_length": 512, "pad_token": "[PAD]", "sep_token": "[SEP]", "tokenizer_class": "<PERSON><PERSON><PERSON><PERSON>", "trim_offsets": true, "unk_token": "[UNK]"}