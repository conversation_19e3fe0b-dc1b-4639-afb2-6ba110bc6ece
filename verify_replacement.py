#!/usr/bin/env python3
"""
验证CrossAttention替换是否成功
"""
import torch
import argparse
import sys
import os

def main():
    try:
        # 导入模型
        from models.core import PMMRNet, CrossAttention, LinearAttention
        
        # 创建模型参数
        args = argparse.Namespace()
        args.decoder_layers = 3
        args.linear_heads = 10
        args.linear_hidden_dim = 32
        args.decoder_heads = 4
        args.encoder_heads = 4
        args.gnn_layers = 3
        args.encoder_layers = 1
        args.decoder_nums = 1
        args.decoder_dim = 128
        args.compound_gnn_dim = 78
        args.pf_dim = 1024
        args.dropout = 0.2
        args.protein_dim = 128
        args.compound_structure_dim = 78
        args.compound_text_dim = 128
        args.compound_pretrained_dim = 384
        args.protein_pretrained_dim = 480
        args.objective = 'regression'
        
        # 创建模型
        model = PMMRNet(args)
        
        # 验证替换
        success = True
        
        if isinstance(model.drug_attn, CrossAttention):
            print("✓ drug_attn 已成功替换为 CrossAttention")
        else:
            print("✗ drug_attn 未正确替换")
            success = False
            
        if isinstance(model.target_attn, CrossAttention):
            print("✓ target_attn 已成功替换为 CrossAttention")
        else:
            print("✗ target_attn 未正确替换")
            success = False
            
        if isinstance(model.inter_attn_one, CrossAttention):
            print("✓ inter_attn_one 已成功替换为 CrossAttention")
        else:
            print("✗ inter_attn_one 未正确替换")
            success = False
        
        # 显示调整后的heads数量
        print(f"drug_attn heads: {model.drug_attn.heads} (原始: {args.linear_heads})")
        print(f"target_attn heads: {model.target_attn.heads} (原始: {args.linear_heads})")
        print(f"inter_attn_one heads: {model.inter_attn_one.heads} (原始: {args.linear_heads})")
        
        if success:
            print("\n🎉 所有LinearAttention已成功替换为CrossAttention！")
            
            # 简单的前向传播测试
            print("\n测试前向传播...")
            model.eval()
            
            batch_size = 1
            compound_nodes = 10
            protein_length = 20
            smiles_length = 15
            
            data = {
                'COMPOUND_NODE_FEAT': torch.randn(batch_size, compound_nodes, args.compound_structure_dim),
                'COMPOUND_ADJ': torch.rand(batch_size, compound_nodes, compound_nodes),
                'COMPOUND_EMBEDDING': torch.randn(batch_size, smiles_length, args.compound_pretrained_dim),
                'PROTEIN_EMBEDDING': torch.randn(batch_size, protein_length, args.protein_pretrained_dim),
                'COMPOUND_NODE_NUM': torch.tensor([compound_nodes]),
                'COMPOUND_SMILES_LENGTH': torch.tensor([smiles_length]),
                'PROTEIN_NODE_NUM': torch.tensor([protein_length]),
                'LABEL': torch.randn(batch_size, 1)
            }
            
            with torch.no_grad():
                output = model(data)
                print(f"✓ 前向传播成功，输出形状: {output.shape}")
                
            print("\n✅ 替换完成且功能正常！")
            return True
        else:
            print("\n❌ 替换未完全成功")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n=== 替换总结 ===")
        print("✓ LinearAttention -> CrossAttention 替换成功")
        print("✓ 保持了相同的接口和输出格式")
        print("✓ 增加了多头注意力机制和交互能力")
        print("✓ 自动调整heads数量以适应input_dim")
    else:
        print("\n需要修复问题后重新测试")
    
    sys.exit(0 if success else 1)
