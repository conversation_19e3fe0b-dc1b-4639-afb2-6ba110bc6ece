#!/usr/bin/env python3
"""
快速测试CrossAttention
"""
import torch
import argparse
from models.core import CrossAttention, PMMRNet

def test_cross_attention():
    print("测试CrossAttention...")
    
    # 测试不同的参数组合
    test_cases = [
        (128, 32, 10),  # 原始参数
        (128, 32, 8),   # 能整除的参数
        (256, 64, 16),  # 更大的参数
    ]
    
    for input_dim, hidden_dim, heads in test_cases:
        print(f"\n测试参数: input_dim={input_dim}, hidden_dim={hidden_dim}, heads={heads}")
        
        try:
            # 创建CrossAttention
            cross_attn = CrossAttention(input_dim, hidden_dim, heads)
            
            # 创建测试数据
            batch_size = 2
            seq_len = 10
            x = torch.randn(batch_size, seq_len, input_dim)
            masks = torch.ones(batch_size, cross_attn.heads, seq_len)
            
            # 前向传播
            output = cross_attn(x, masks)
            print(f"✓ 成功，输出形状: {output.shape}")
            
        except Exception as e:
            print(f"✗ 失败: {e}")

def test_model():
    print("\n测试PMMRNet模型...")
    
    # 创建模型参数
    args = argparse.Namespace()
    args.decoder_layers = 3
    args.linear_heads = 10
    args.linear_hidden_dim = 32
    args.decoder_heads = 4
    args.encoder_heads = 4
    args.gnn_layers = 3
    args.encoder_layers = 1
    args.decoder_nums = 1
    args.decoder_dim = 128
    args.compound_gnn_dim = 78
    args.pf_dim = 1024
    args.dropout = 0.2
    args.protein_dim = 128
    args.compound_structure_dim = 78
    args.compound_text_dim = 128
    args.compound_pretrained_dim = 384
    args.protein_pretrained_dim = 480
    args.objective = 'regression'
    
    try:
        # 创建模型
        model = PMMRNet(args)
        print("✓ 模型创建成功")
        
        # 检查注意力层
        print(f"drug_attn heads: {model.drug_attn.heads}")
        print(f"target_attn heads: {model.target_attn.heads}")
        print(f"inter_attn_one heads: {model.inter_attn_one.heads}")
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_cross_attention()
    test_model()
    print("\n测试完成！")
