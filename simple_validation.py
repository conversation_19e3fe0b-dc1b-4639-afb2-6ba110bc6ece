import torch
import argparse
from models.core import PMMRNet

# 创建模型参数
args = argparse.Namespace()
args.decoder_layers = 3
args.linear_heads = 10
args.linear_hidden_dim = 32
args.decoder_heads = 4
args.encoder_heads = 4
args.gnn_layers = 3
args.encoder_layers = 1
args.decoder_nums = 1
args.decoder_dim = 128
args.compound_gnn_dim = 78
args.pf_dim = 1024
args.dropout = 0.2
args.protein_dim = 128
args.compound_structure_dim = 78
args.compound_text_dim = 128
args.compound_pretrained_dim = 384
args.protein_pretrained_dim = 480
args.objective = 'regression'

# 创建模型
model = PMMRNet(args)
print("Model created successfully")
print(f"drug_attn heads: {model.drug_attn.heads}")
print(f"target_attn heads: {model.target_attn.heads}")
print(f"inter_attn_one heads: {model.inter_attn_one.heads}")

# 创建测试数据
batch_size = 1
compound_nodes = 5
protein_length = 10
smiles_length = 8

data = {
    'COMPOUND_NODE_FEAT': torch.randn(batch_size, compound_nodes, args.compound_structure_dim),
    'COMPOUND_ADJ': torch.rand(batch_size, compound_nodes, compound_nodes),
    'COMPOUND_EMBEDDING': torch.randn(batch_size, smiles_length, args.compound_pretrained_dim),
    'PROTEIN_EMBEDDING': torch.randn(batch_size, protein_length, args.protein_pretrained_dim),
    'COMPOUND_NODE_NUM': torch.tensor([compound_nodes]),
    'COMPOUND_SMILES_LENGTH': torch.tensor([smiles_length]),
    'PROTEIN_NODE_NUM': torch.tensor([protein_length]),
    'LABEL': torch.randn(batch_size, 1)
}

# 前向传播
model.eval()
with torch.no_grad():
    output = model(data)
    print(f"Forward pass successful, output shape: {output.shape}")

print("All tests passed!")
