#!/usr/bin/env python3
"""
下载ChemBERTa模型的辅助脚本
如果遇到网络问题，可以尝试不同的下载方法
"""

import os
import sys
from transformers import AutoTokenizer, RobertaModel
import argparse

def download_model_huggingface(model_name, save_path):
    """使用HuggingFace transformers库下载模型"""
    print(f"正在从HuggingFace下载模型: {model_name}")
    print(f"保存路径: {save_path}")
    
    try:
        # 下载tokenizer
        print("下载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(save_path)
        
        # 下载模型
        print("下载模型...")
        model = RobertaModel.from_pretrained(model_name)
        model.save_pretrained(save_path)
        
        print(f"模型成功下载到: {save_path}")
        return True
        
    except Exception as e:
        print(f"下载失败: {e}")
        return False

def download_with_git_lfs(model_name, save_path):
    """使用git lfs下载模型（需要安装git和git-lfs）"""
    print(f"尝试使用git lfs下载模型: {model_name}")
    
    try:
        import subprocess
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 使用git clone下载
        repo_url = f"https://huggingface.co/{model_name}"
        cmd = ["git", "clone", repo_url, save_path]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"模型成功下载到: {save_path}")
            return True
        else:
            print(f"git clone失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"git lfs下载失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="下载ChemBERTa模型")
    parser.add_argument("--model_name", type=str, default="DeepChem/ChemBERTa-77M-MLM",
                       help="模型名称")
    parser.add_argument("--save_path", type=str, default="./models/ChemBERTa-77M-MLM",
                       help="保存路径")
    parser.add_argument("--method", type=str, choices=["huggingface", "git"], 
                       default="huggingface", help="下载方法")
    
    args = parser.parse_args()
    
    print("ChemBERTa模型下载工具")
    print("=" * 50)
    print(f"模型名称: {args.model_name}")
    print(f"保存路径: {args.save_path}")
    print(f"下载方法: {args.method}")
    print("=" * 50)
    
    # 创建保存目录
    os.makedirs(args.save_path, exist_ok=True)
    
    success = False
    
    if args.method == "huggingface":
        success = download_model_huggingface(args.model_name, args.save_path)
    elif args.method == "git":
        success = download_with_git_lfs(args.model_name, args.save_path)
    
    if not success:
        print("\n下载失败！可能的解决方案：")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN")
        print("3. 使用git方法: python download_model.py --method git")
        print("4. 手动从以下网址下载:")
        print(f"   https://huggingface.co/{args.model_name}")
        print("5. 设置代理环境变量:")
        print("   export HTTP_PROXY=http://your-proxy:port")
        print("   export HTTPS_PROXY=https://your-proxy:port")
        sys.exit(1)
    else:
        print(f"\n成功！现在可以使用以下命令运行预处理:")
        print(f"python compound_pretrain.py --model_path {args.save_path}")

if __name__ == "__main__":
    main()
