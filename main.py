import sys, os
import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader, Dataset
import argparse
from data import CPIDataset
from models.core import *
from utils import *
from sklearn.metrics import precision_recall_curve, roc_curve, auc, f1_score, accuracy_score, roc_auc_score, average_precision_score
import random
import torch.backends.cudnn as cudnn
from sklearn.metrics import auc
from sklearn.metrics import RocCurveDisplay
import csv
from datetime import datetime
import warnings
from data_loader_fix import create_safe_dataloader, setup_multiprocessing
try:
    from rdkit import RDLogger
    # Disable all RDKit warnings (including the frequent GetValence deprecation messages)
    RDLogger.DisableLog('rdApp.*')
except Exception:
    # RDKit may not be installed or available in all code paths; ignore if import fails
    pass
# Also silence Python-level DeprecationWarning (in case some libs raise them via warnings module)
warnings.filterwarnings("ignore", category=DeprecationWarning)

os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'

# training function at each epoch
def train_dta(model, loss_fn, train_loader, optimizer, epoch):
    print('Training on {} samples...'.format(len(train_loader.dataset)))
    if hasattr(torch.cuda, 'empty_cache'):
        torch.cuda.empty_cache()

    model.train()

    for batch_idx, data in enumerate(train_loader):
        try:
            optimizer.zero_grad()
            output = model(data)
            loss = loss_fn(output, data['LABEL'].view(-1, 1).float().cuda())
            loss.backward()
            optimizer.step()

            # 定期清理GPU内存
            if batch_idx % 10 == 0:
                torch.cuda.empty_cache()

            if batch_idx % 20 == 0:
                print('Train epoch: {} [{}/{} ({:.0f}%)]\tLoss: {:.6f}'.format(epoch,
                                                                               batch_idx * len(data['LABEL']),
                                                                               len(train_loader.dataset),
                                                                               100. * batch_idx / len(train_loader),
                                                                               loss.item()))
        except torch.cuda.OutOfMemoryError:
            print(f"CUDA out of memory at batch {batch_idx}. Clearing cache and skipping batch.")
            torch.cuda.empty_cache()
            continue

def predicting_dta(model, loader):
    model.eval()
    total_preds = torch.Tensor()
    total_labels = torch.Tensor()
    print('Make prediction for {} samples...'.format(len(loader.dataset)))
    with torch.no_grad():
        for data in loader:
            output = model(data)
            total_preds = torch.cat((total_preds, output.cpu()), 0)
            total_labels = torch.cat((total_labels, data['LABEL'].view(-1, 1).cpu()), 0)
    return total_labels.numpy().flatten(),total_preds.numpy().flatten()


def train_dti(model, loss_fn, train_loader, optimizer, epoch):
    print('Training on {} samples...'.format(len(train_loader.dataset)))
    if hasattr(torch.cuda, 'empty_cache'):
        torch.cuda.empty_cache()

    model.train()
    for batch_idx, data in enumerate(train_loader):
        try:
            optimizer.zero_grad()
            output = model(data)
            loss = loss_fn(output, data['LABEL'].long().cuda())
            loss.backward()
            optimizer.step()

            # 定期清理GPU内存
            if batch_idx % 10 == 0:
                torch.cuda.empty_cache()

            if batch_idx % 20 == 0:
                print('Train epoch: {} [{}/{} ({:.0f}%)]\tLoss: {:.6f}'.format(epoch,
                                                                               batch_idx * len(data['LABEL']),
                                                                               len(train_loader.dataset),
                                                                               100. * batch_idx / len(train_loader),
                                                                               loss.item()))
        except torch.cuda.OutOfMemoryError:
            print(f"CUDA out of memory at batch {batch_idx}. Clearing cache and skipping batch.")
            torch.cuda.empty_cache()
            continue

def predicting_dti(model, loader):
    model.eval()
    total_preds = []
    total_labels = []

    print('Making predictions for {} samples...'.format(len(loader.dataset)))
    with torch.no_grad():
        for data in loader:
            output = model(data)
            total_preds.append(output.cpu().numpy())
            total_labels.append(data['LABEL'].long().cpu().numpy())

    return np.concatenate(total_labels), np.concatenate(total_preds)


def davis_dataloader(seed=0, batch_size=256, workers=4, dataset = 'davis', data_path='./data'):
    print('\nrunning on ', dataset)

    path = data_path + '/' + dataset

    data = CPIDataset(f'{path}.csv', f'{path}/compound', f'{path}/protein')

    print(len(data))

    test_size = (int)(len(data) * 0.1)

    train_dataset, test_dataset = torch.utils.data.random_split(
        dataset=data,
        lengths=[len(data) - (test_size * 2), test_size * 2],
        generator=torch.Generator().manual_seed(seed)
    )
    val_dataset, test_dataset = torch.utils.data.random_split(
        dataset=test_dataset,
        lengths=[test_size, test_size],
        generator=torch.Generator().manual_seed(seed)
    )

    # 使用安全的DataLoader创建函数
    train_loader = create_safe_dataloader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=data.collate_fn,
    )

    val_loader = create_safe_dataloader(
        val_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=data.collate_fn,
    )

    test_loader = create_safe_dataloader(
        test_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=data.collate_fn,
    )

    return train_loader, val_loader, test_loader

def others_dataloader(batch_size, workers=4, dataset = 'davis', data_path='./data'):
    print('\nrunning on ', dataset)

    path = data_path + '/' + dataset

    if dataset == 'bindingdb':
        train_set = CPIDataset(f'{path}_train.csv', f'{path}/compound', f'{path}/protein/train')
        test_set = CPIDataset(f'{path}_test.csv',f'{path}/compound', f'{path}/protein/test')

    else:

        train_set = CPIDataset(f'{path}_train.csv', f'{path}/compound', f'{path}/protein')

        test_set = CPIDataset(f'{path}_test.csv',f'{path}/compound', f'{path}/protein')

    # 使用安全的DataLoader创建函数
    train_loader = create_safe_dataloader(
        train_set,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=train_set.collate_fn,
    )

    test_loader = create_safe_dataloader(
        test_set,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=test_set.collate_fn,
    )

    return train_loader, test_loader



def run(args: argparse.Namespace):
    # 设置多进程环境
    setup_multiprocessing()

    # 设置CUDA内存管理
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

    data_path = args.root_data_path
    dataset = args.dataset
    batch_size = args.batch_size
    LR = args.learning_rate
    NUM_EPOCHS = args.max_epochs
    seed = args.seed
    output_dir = getattr(args, 'output_dir', './outputs')
    os.makedirs(output_dir, exist_ok=True)

    model = PMMRNet(args).cuda()

    print('Learning rate: ', LR)
    print('Epochs: ', NUM_EPOCHS)

    # Main program: iterate over different datasets
    if dataset == 'davis':
        train_loader, val_loader, test_loader = davis_dataloader(seed, batch_size, 4, dataset, data_path)
    else:
        train_loader,test_loader = others_dataloader(batch_size, 4, dataset, data_path)

    if args.objective == 'regression':
        loss_fn = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=LR)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=30,
                                                               eps=1e-08)
        best_mae = 1000
        best_ci = 0
        best_epoch = -1
        model_file_name = os.path.join(output_dir, 'model_' + dataset + '.pth')
        result_file_name = os.path.join(output_dir, 'result_' + dataset + '.csv')
        history_file_name = os.path.join(output_dir, 'history_' + dataset + '.csv')
        eval_split = 'val' if dataset == 'davis' else 'test'
        for epoch in range(NUM_EPOCHS):

            train_dta(model, loss_fn, train_loader, optimizer, epoch + 1)

            if dataset == 'davis':
                G, P = predicting_dta(model, val_loader)
            else:
                G, P = predicting_dta(model, test_loader)
            ret = [rmse(G, P), mse(G, P), pearson(G, P), spearman(G, P), ci(G, P)]
            current_mae = mae(G, P)
            # Append per-epoch history with labeled metrics
            is_best = ret[4] > best_ci
            timestamp = datetime.now().isoformat(timespec='seconds')
            exists_before = os.path.exists(history_file_name) and os.path.getsize(history_file_name) > 0
            header_has_mae = False
            if exists_before:
                try:
                    with open(history_file_name, 'r', newline='') as rf:
                        reader = csv.reader(rf)
                        first_row = next(reader, None)
                        if first_row and ('mae' in first_row):
                            header_has_mae = True
                except Exception:
                    pass
            with open(history_file_name, 'a', newline='') as f:
                writer = csv.writer(f)
                if not exists_before:
                    # 新文件，写入包含 MAE 的表头
                    writer.writerow(['timestamp', 'epoch', 'dataset', 'objective', 'split', 'rmse', 'mse', 'mae', 'pearson', 'spearman', 'ci', 'is_best'])
                    writer.writerow([timestamp, epoch + 1, dataset, args.objective, eval_split, ret[0], ret[1], float(current_mae), ret[2], ret[3], ret[4], is_best])
                else:
                    # 兼容：若旧表头无 mae，则按旧格式写入；否则按新格式写入
                    if header_has_mae:
                        writer.writerow([timestamp, epoch + 1, dataset, args.objective, eval_split, ret[0], ret[1], float(current_mae), ret[2], ret[3], ret[4], is_best])
                    else:
                        writer.writerow([timestamp, epoch + 1, dataset, args.objective, eval_split, ret[0], ret[1], ret[2], ret[3], ret[4], is_best])
            if is_best:
                torch.save(model.state_dict(), model_file_name)
                with open(result_file_name, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['rmse', 'mse', 'mae', 'pearson', 'spearman', 'ci'])
                    writer.writerow([ret[0], ret[1], float(current_mae), ret[2], ret[3], ret[4]])
                best_epoch = epoch + 1
                best_ci = ret[4]
                best_mae = float(current_mae)
                print(f'rmse improved at epoch {best_epoch}; best_ci,best_mae: {best_ci:.6f} {best_mae:.6f} {model_file_name}')
            else:
                print(f'{ret[4]:.6f} No improvement since epoch {best_epoch}; best_ci,best_mae: {best_ci:.6f} {best_mae:.6f}')
            scheduler.step(ret[0])
            if epoch - best_epoch > 60:
                print("Early stopping")
                break

    else:
        loss_fn = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=LR)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.1, patience=30,
                                                               eps=1e-08)
        best_auc = 0
        best_epoch = -1
        model_file_name = os.path.join(output_dir, 'model_' + dataset + '.pth')
        result_file_name = os.path.join(output_dir, 'result_' + dataset + '.csv')
        history_file_name = os.path.join(output_dir, 'history_' + dataset + '.csv')
        eval_split = 'val' if dataset == 'davis' else 'test'
        for epoch in range(NUM_EPOCHS):

            train_dti(model, loss_fn, train_loader, optimizer, epoch + 1)

            if dataset == 'davis':
                G, P = predicting_dti(model, val_loader)
            else:
                G, P = predicting_dti(model, test_loader)

            ret = [roc_auc_score(G, P[:, 1]), average_precision_score(G, P[:, 1]),
                   f1_score(G, np.argmax(P, axis=1)), accuracy_score(G, np.argmax(P, axis=1))]
            # Append per-epoch history with labeled metrics
            is_best = ret[0] > best_auc
            timestamp = datetime.now().isoformat(timespec='seconds')
            exists_before = os.path.exists(history_file_name) and os.path.getsize(history_file_name) > 0
            with open(history_file_name, 'a', newline='') as f:
                writer = csv.writer(f)
                if not exists_before:
                    writer.writerow(['timestamp', 'epoch', 'dataset', 'objective', 'split', 'auc', 'aupr', 'f1', 'acc', 'is_best'])
                writer.writerow([timestamp, epoch + 1, dataset, args.objective, eval_split, ret[0], ret[1], ret[2], ret[3], is_best])
            if is_best:
                torch.save(model.state_dict(), model_file_name)
                with open(result_file_name, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['auc', 'aupr', 'f1', 'acc'])
                    writer.writerow(ret)
                best_epoch = epoch + 1
                best_auc = ret[0]
                print('auc improved at epoch ', best_epoch, '; best_auc:', best_auc, model_file_name)
            else:
                print(ret[0], 'No improvement since epoch ', best_epoch, '; best_auc:', best_auc)
            scheduler.step(ret[0])
            if epoch - best_epoch > 60:
                print("Early stopping")
                break


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='PMMR')
    parser.add_argument('--dataset', type=str, default='davis', help='dataset')
    parser.add_argument('--root_data_path', type=str, default='./data', help='data path')
    parser.add_argument('--output_dir', type=str, default='./outputs', help='directory to save models and results')
    parser.add_argument('--batch_size', type=int, default=256, help='batch size')
    parser.add_argument('--learning_rate', type=float, default=0.0001, help='learning rate')
    parser.add_argument('--max_epochs', type=int, default=1000, help='max epochs')
    parser.add_argument('--num_workers', type=int, default=4, help='num workers')
    parser.add_argument('--seed', type=int, default=0, help='seed')
    parser.add_argument('--objective', type=str, default='regression', help='objective')

    # model parameters
    parser.add_argument('--decoder_layers', type=int, default=3, help='decoder layers')
    parser.add_argument('--linear_heads', type=int, default=10, help='linear heads')
    parser.add_argument('--linear_hidden_dim', type=int, default=32, help='linear hidden dimension')
    parser.add_argument('--decoder_heads', type=int, default=4, help='decoder heads')
    parser.add_argument('--encoder_heads', type=int, default=4, help='encoder heads')
    parser.add_argument('--gnn_layers', type=int, default=3, help='gnn layers')
    parser.add_argument('--encoder_layers', type=int, default=1, help='encoder layers')
    parser.add_argument('--decoder_nums', type=int, default=1, help='decoder nums')
    parser.add_argument('--decoder_dim', type=int, default=128, help='decoder dimension')
    parser.add_argument('--compound_gnn_dim', type=int, default=78, help='compound gnn dimension')
    parser.add_argument('--pf_dim', type=int, default=1024, help='feedforward dimension')
    parser.add_argument('--dropout', type=float, default=0.2, help='dropout')
    parser.add_argument('--protein_dim', type=int, default=128, help='protein dimension')
    parser.add_argument('--compound_structure_dim', type=int, default=78, help='compound structure dimension')
    parser.add_argument('--compound_text_dim', type=int, default=128, help='compound text dimension')
    parser.add_argument('--compound_pretrained_dim', type=int, default=384, help='compound pretrained dimension')
    parser.add_argument('--protein_pretrained_dim', type=int, default=480, help='protein pretrained dimension')

    args = parser.parse_args()
    print("Arguments parsed:", args)

    # Set random seed for reproducibility
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)

    print("Starting run function...")
    run(args)













