#!/usr/bin/env python3
"""
测试CrossAttention替换LinearAttention后的模型兼容性
"""
import torch
import argparse
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from models.core import PMMRNet, LinearAttention, CrossAttention
    print("✓ 成功导入模型类")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

def test_cross_attention_basic():
    """测试CrossAttention基本功能"""
    print("\n=== 测试CrossAttention基本功能 ===")
    
    # 测试参数
    batch_size = 2
    seq_len = 10
    input_dim = 128
    hidden_dim = 32
    heads = 8
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, input_dim)
    masks = torch.ones(batch_size, heads, seq_len)
    
    # 测试CrossAttention
    try:
        cross_attn = CrossAttention(input_dim, hidden_dim, heads)
        output = cross_attn(x, masks)
        print(f"✓ CrossAttention输出形状: {output.shape}")
        print(f"✓ 期望输出形状: ({batch_size}, {input_dim})")
        
        # 验证输出形状
        expected_shape = (batch_size, input_dim)
        if output.shape == expected_shape:
            print("✓ 输出形状正确")
        else:
            print(f"✗ 输出形状错误，期望 {expected_shape}，实际 {output.shape}")
            return False
            
    except Exception as e:
        print(f"✗ CrossAttention测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_linear_attention_comparison():
    """比较LinearAttention和CrossAttention的接口兼容性"""
    print("\n=== 测试接口兼容性 ===")
    
    # 测试参数
    batch_size = 2
    seq_len = 10
    input_dim = 128
    hidden_dim = 32
    heads = 8
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, input_dim)
    masks = torch.ones(batch_size, heads, seq_len)
    
    try:
        # 测试LinearAttention
        linear_attn = LinearAttention(input_dim, hidden_dim, heads)
        linear_output = linear_attn(x, masks)
        print(f"✓ LinearAttention输出形状: {linear_output.shape}")
        
        # 测试CrossAttention
        cross_attn = CrossAttention(input_dim, hidden_dim, heads)
        cross_output = cross_attn(x, masks)
        print(f"✓ CrossAttention输出形状: {cross_output.shape}")
        
        # 验证形状一致性
        if linear_output.shape == cross_output.shape:
            print("✓ 两种注意力机制输出形状一致")
        else:
            print(f"✗ 输出形状不一致: Linear {linear_output.shape} vs Cross {cross_output.shape}")
            return False
            
    except Exception as e:
        print(f"✗ 接口兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_model_initialization():
    """测试模型初始化"""
    print("\n=== 测试模型初始化 ===")
    
    # 创建模型参数
    args = argparse.Namespace()
    args.decoder_layers = 3
    args.linear_heads = 8  # 修改为8，确保能被input_dim整除
    args.linear_hidden_dim = 32
    args.decoder_heads = 4
    args.encoder_heads = 4
    args.gnn_layers = 3
    args.encoder_layers = 1
    args.decoder_nums = 1
    args.decoder_dim = 128
    args.compound_gnn_dim = 78
    args.pf_dim = 1024
    args.dropout = 0.2
    args.protein_dim = 128
    args.compound_structure_dim = 78
    args.compound_text_dim = 128
    args.compound_pretrained_dim = 384
    args.protein_pretrained_dim = 480
    args.objective = 'regression'
    
    try:
        # 创建模型
        model = PMMRNet(args)
        print("✓ 模型初始化成功")
        
        # 检查模型参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"✓ 模型总参数数量: {total_params:,}")
        
        # 检查CrossAttention实例
        if isinstance(model.drug_attn, CrossAttention):
            print("✓ drug_attn 已替换为 CrossAttention")
        else:
            print("✗ drug_attn 未正确替换")
            return False
            
        if isinstance(model.target_attn, CrossAttention):
            print("✓ target_attn 已替换为 CrossAttention")
        else:
            print("✗ target_attn 未正确替换")
            return False
            
        if isinstance(model.inter_attn_one, CrossAttention):
            print("✓ inter_attn_one 已替换为 CrossAttention")
        else:
            print("✗ inter_attn_one 未正确替换")
            return False
            
    except Exception as e:
        print(f"✗ 模型初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_model_forward():
    """测试模型前向传播"""
    print("\n=== 测试模型前向传播 ===")
    
    # 创建模型参数
    args = argparse.Namespace()
    args.decoder_layers = 3
    args.linear_heads = 8  # 修改为8，确保能被input_dim整除
    args.linear_hidden_dim = 32
    args.decoder_heads = 4
    args.encoder_heads = 4
    args.gnn_layers = 3
    args.encoder_layers = 1
    args.decoder_nums = 1
    args.decoder_dim = 128
    args.compound_gnn_dim = 78
    args.pf_dim = 1024
    args.dropout = 0.2
    args.protein_dim = 128
    args.compound_structure_dim = 78
    args.compound_text_dim = 128
    args.compound_pretrained_dim = 384
    args.protein_pretrained_dim = 480
    args.objective = 'regression'
    
    try:
        # 创建模型
        model = PMMRNet(args)
        model.eval()
        print("✓ 模型创建成功")
        
        # 创建模拟数据
        batch_size = 2
        compound_nodes = 20
        protein_length = 50
        smiles_length = 30
        
        data = {
            'COMPOUND_NODE_FEAT': torch.randn(batch_size, compound_nodes, args.compound_structure_dim),
            'COMPOUND_ADJ': torch.rand(batch_size, compound_nodes, compound_nodes),
            'COMPOUND_EMBEDDING': torch.randn(batch_size, smiles_length, args.compound_pretrained_dim),
            'PROTEIN_EMBEDDING': torch.randn(batch_size, protein_length, args.protein_pretrained_dim),
            'COMPOUND_NODE_NUM': torch.tensor([compound_nodes, compound_nodes-5]),
            'COMPOUND_SMILES_LENGTH': torch.tensor([smiles_length, smiles_length-5]),
            'PROTEIN_NODE_NUM': torch.tensor([protein_length, protein_length-10]),
            'LABEL': torch.randn(batch_size, 1)
        }
        
        # 前向传播
        with torch.no_grad():
            output = model(data)
            print(f"✓ 前向传播成功，输出形状: {output.shape}")
            
            # 验证输出形状
            if args.objective == 'regression':
                expected_shape = (batch_size, 1)
            else:
                expected_shape = (batch_size, 2)
                
            if output.shape == expected_shape:
                print("✓ 输出形状正确")
            else:
                print(f"✗ 输出形状错误，期望 {expected_shape}，实际 {output.shape}")
                return False
                
    except Exception as e:
        print(f"✗ 模型前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试CrossAttention替换LinearAttention的兼容性...")
    
    tests = [
        test_cross_attention_basic,
        test_linear_attention_comparison,
        test_model_initialization,
        test_model_forward
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ 测试通过")
            else:
                print("✗ 测试失败")
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！CrossAttention替换成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复问题")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
