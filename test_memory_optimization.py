#!/usr/bin/env python3
"""
测试内存优化后的CrossAttention
"""
import torch
import argparse
import gc
from models.core import PMMRNet, CrossAttention

def get_memory_usage():
    """获取当前GPU内存使用情况"""
    if torch.cuda.is_available():
        return torch.cuda.memory_allocated() / 1024**2  # MB
    return 0

def test_memory_efficiency():
    print("测试内存优化效果...")
    
    # 测试不同batch size的内存使用
    batch_sizes = [16, 32, 64]
    
    for batch_size in batch_sizes:
        print(f"\n测试 batch_size = {batch_size}")
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        initial_memory = get_memory_usage()
        print(f"初始内存: {initial_memory:.1f} MB")
        
        try:
            # 创建模型参数
            args = argparse.Namespace()
            args.decoder_layers = 3
            args.linear_heads = 10
            args.linear_hidden_dim = 32
            args.decoder_heads = 4
            args.encoder_heads = 4
            args.gnn_layers = 3
            args.encoder_layers = 1
            args.decoder_nums = 1
            args.decoder_dim = 128
            args.compound_gnn_dim = 78
            args.pf_dim = 1024
            args.dropout = 0.2
            args.protein_dim = 128
            args.compound_structure_dim = 78
            args.compound_text_dim = 128
            args.compound_pretrained_dim = 384
            args.protein_pretrained_dim = 480
            args.objective = 'regression'
            
            # 创建模型
            model = PMMRNet(args)
            if torch.cuda.is_available():
                model = model.cuda()
            
            model_memory = get_memory_usage()
            print(f"模型加载后内存: {model_memory:.1f} MB")
            
            # 创建测试数据
            compound_nodes = 20
            protein_length = 50
            smiles_length = 30
            
            data = {
                'COMPOUND_NODE_FEAT': torch.randn(batch_size, compound_nodes, args.compound_structure_dim),
                'COMPOUND_ADJ': torch.rand(batch_size, compound_nodes, compound_nodes),
                'COMPOUND_EMBEDDING': torch.randn(batch_size, smiles_length, args.compound_pretrained_dim),
                'PROTEIN_EMBEDDING': torch.randn(batch_size, protein_length, args.protein_pretrained_dim),
                'COMPOUND_NODE_NUM': torch.tensor([compound_nodes] * batch_size),
                'COMPOUND_SMILES_LENGTH': torch.tensor([smiles_length] * batch_size),
                'PROTEIN_NODE_NUM': torch.tensor([protein_length] * batch_size),
                'LABEL': torch.randn(batch_size, 1)
            }
            
            if torch.cuda.is_available():
                for key in data:
                    if key != 'LABEL':
                        data[key] = data[key].cuda()
            
            data_memory = get_memory_usage()
            print(f"数据加载后内存: {data_memory:.1f} MB")
            
            # 前向传播
            model.eval()
            with torch.no_grad():
                output = model(data)
                
            forward_memory = get_memory_usage()
            print(f"前向传播后内存: {forward_memory:.1f} MB")
            print(f"✓ 成功完成 batch_size={batch_size}，输出形状: {output.shape}")
            
            # 清理
            del model, data, output
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"✗ batch_size={batch_size} 内存溢出: {e}")
            else:
                print(f"✗ batch_size={batch_size} 其他错误: {e}")
        except Exception as e:
            print(f"✗ batch_size={batch_size} 未知错误: {e}")

def test_cross_attention_directly():
    print("\n直接测试CrossAttention内存使用...")
    
    # 测试不同的序列长度和batch size
    test_cases = [
        (32, 20, 128),   # (batch_size, seq_len, input_dim)
        (64, 30, 128),
        (64, 50, 128),
    ]
    
    for batch_size, seq_len, input_dim in test_cases:
        print(f"\n测试 batch_size={batch_size}, seq_len={seq_len}, input_dim={input_dim}")
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        try:
            # 创建CrossAttention
            cross_attn = CrossAttention(input_dim, 32, 10)
            if torch.cuda.is_available():
                cross_attn = cross_attn.cuda()
            
            # 创建测试数据
            x = torch.randn(batch_size, seq_len, input_dim)
            masks = torch.ones(batch_size, cross_attn.heads, seq_len)
            
            if torch.cuda.is_available():
                x = x.cuda()
                masks = masks.cuda()
            
            # 前向传播
            cross_attn.eval()
            with torch.no_grad():
                output = cross_attn(x, masks)
            
            print(f"✓ 成功，输出形状: {output.shape}")
            print(f"  实际使用heads: {cross_attn.heads}")
            print(f"  投影维度: {cross_attn.proj_dim}")
            
            # 清理
            del cross_attn, x, masks, output
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            
        except Exception as e:
            print(f"✗ 失败: {e}")

def main():
    print("开始测试内存优化...")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"总显存: {torch.cuda.get_device_properties(0).total_memory / 1024**2:.0f} MB")
    else:
        print("使用CPU测试")
    
    test_cross_attention_directly()
    test_memory_efficiency()
    
    print("\n内存优化测试完成！")

if __name__ == '__main__':
    main()
