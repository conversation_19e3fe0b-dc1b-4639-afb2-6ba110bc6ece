#!/usr/bin/env python3
"""
快速测试修复是否有效
"""
import os
import torch
import platform
from data_loader_fix import create_safe_dataloader

def main():
    print("快速测试开始...")
    
    # 1. 设置环境变量
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    print("✓ 环境变量设置完成")
    
    # 2. 检查CUDA
    if torch.cuda.is_available():
        print(f"✓ CUDA可用，设备数量: {torch.cuda.device_count()}")
        print(f"✓ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("⚠ CUDA不可用")
    
    # 3. 测试DataLoader
    try:
        class SimpleDataset(torch.utils.data.Dataset):
            def __len__(self):
                return 10
            def __getitem__(self, idx):
                return {'data': torch.randn(5), 'label': idx}
        
        dataset = SimpleDataset()
        loader = create_safe_dataloader(dataset, batch_size=4)
        
        # 测试一个批次
        batch = next(iter(loader))
        print(f"✓ DataLoader测试成功，批次大小: {batch['data'].shape[0]}")
        
    except Exception as e:
        print(f"✗ DataLoader测试失败: {e}")
        return False
    
    print("\n所有测试通过！可以运行主程序了。")
    print("建议使用: python run_with_memory_fix.py --batch_size 32")
    return True

if __name__ == '__main__':
    main()
