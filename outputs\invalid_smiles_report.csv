timestamp,file,row_index,smiles,reason
2025-09-11T20:07:40,./data\pdb_test.csv,59,P(=O)(=O)(OC[C@H]1O[C@@H](n2c3ncnc(N)c3nc2)[C@H](O)[C@@H]1O[P](=O)(=O)=O)OP(=O)(=O)O[C@@H]1[C@@H](CO[P](=O)(=O)=O)O[C@H](C1)n1cc(c(=O)[nH]c1=O)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_test.csv,76,[NH3+][C@H](C/C=C\N[C+](=N)N)C(=O)N1[C@H](C(=O)N2[C@H](C(=O)N[C@H](C)C(=O)N[C@@H](Cc3ccc(cc3)C)C(=O)N)CCC2)C[C@H]2[C@@H]1CCCC2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_test.csv,170,n1(c(=O)[nH]c(=O)cc1)[C@@H]1O[C@H](CO)[C@@H](O[P](=O)(=O)=O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_test.csv,182,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_test.csv,184,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_test.csv,193,O=C([C@H](Cc1onc(c1)c1ccc(cc1)c1cc(Cl)ccc1)CP(=O)(=O)c1ccc(Br)cc1)N[C@H](C(=O)N[C@H](C(=O)N)C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_test.csv,203,c1ccccc1Oc1cc(ccc1)CCCNC(=O)C[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5,c1ccc(cc1)[C@@](n1c2c(cccc2)nn1)(Cc1ccc(cc1)c1cc(ccc1)[P](=O)(=O)=O)Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7,C(C(C)C)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CC(C)C)P(=O)(=O)O[C@@H](Cc1ccccc1)C(=O)OC)C(C)C)C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,34,N1([C@H]([C@@H](c2cc(ccc12)OCCC[P](=O)(=O)=O)CC(=O)N)CC)Cc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,57,N(C(=O)c1occc1)[C@@H](CC(C)C)C(=O)N[C@@H](Cc1c2c([nH]c1)cccc2)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,65,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,67,O=[P](=O)(=O)c1ccccc1OCC(=O)Nc1cc(C(F)(F)F)ccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,68,n1(c(=O)[nH]c(=O)c(c1)I)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,73,[C@H]1([C@@H]([C@@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,87,[P](=O)(=O)(=O)O[C@@H]1[C@@H](CO)O[C@H](C1)n1c(=O)[nH]c(=O)c(C)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,98,P(=O)(=O)(OC[C@@H](OC(=O)CCCCCCCCCCCCCCC)COC(=O)CCCCCCCCCCCCCCC)O[C@@H]1[C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,120,O=[P](=O)(=O)CO[C@@H](CO)Cn1ccc(=N)[nH]c1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,122,O=[P](=O)(=O)C(O)([P](=O)(=O)=O)Cc1cccnc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,126,O=c1[nH]c(=O)cc(CO)n1[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,128,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,148,O=P1(=O)OP(=O)(=O)OCC2=C([C@H]([C@H](n3cnc4c(=O)n([C@H]5[C@@H]([C@@H]([C@@H](CO1)O5)O)O)cnc34)O2)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,150,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,156,c1(ccc(cc1)S(=O)(=O)NCCO[P](=O)(=O)=O)OC(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,168,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,181,[NH3+][C@H](C(=O)N[C@H]([C@H](C)O[P](=O)(=O)=O)C(=O)N1CCCC[C@H]1C(=O)N[C@@H](Cc1cc2c(cc1)cccc2)C(=O)N[C@H](C(=O)N)CCC(=O)N)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,201,P(=O)(=O)(S)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,219,c1(ccc(cc1)NC(=O)[P](=O)(=O)=O)CC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,246,O=C(OC[C@H](O)COP(=O)(=O)OCC[NH3+])CCCCCCCCCCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,260,[P](=O)(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,268,n1c(C)c(O)c(CNc2c(=O)[nH]oc2)c(c1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,278,[P](=O)(=O)(=O)O[C@@H]1[C@@H]([C@@H](CO)O[C@H]1n1cnc2c(N)ncnc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,298,[P](=O)(=O)(=O)OP(=O)(=O)CP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,312,C(=O)(OCc1ccccc1)N1CCC[C@H]1C(=O)N[C@@H](CCCC[NH3+])C(=O)N[C@@H](Cc1ccccc1)P(=O)(=O)C[C@H](C(=O)N1CCC[C@H]1C(=O)OC)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,329,[P](=O)(=O)(=O)O[C@@H]1[C@@H](CO)O[C@H](C1)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,336,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,339,C(=O)([C@@H](C(=C)O)C)SCCNC(=O)CCNC(=O)[C@H](O)C(COP(=O)(=O)OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O1)n1c2ncnc(c2nc1)N)O)O[P](=O)(=O)=O)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,354,[P](=O)(=O)(=O)N[C@H](C(=O)N)CC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,361,c12ccccc1oc1c2cccc1c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,362,c12ccccc1oc1c2cccc1c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,392,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,393,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,402,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,415,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,428,O=[P](=O)(=O)CCC[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,432,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,437,O=[P](=O)(=O)OC[C@@H]1[C@@H](O)[C@@H](O)[C@@H](O1)n1ccc(=O)[nH]c1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,447,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,451,O=C(OC)c1ccc(cc1)[C@](n1nnc2ccccc12)(Cc1ccc(C(F)([P](=O)(=O)=O)F)cc1)C/C=C/c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,461,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,464,O=[P](=O)(=O)OC[C@H]1C[C@@H]([C@@H]([C@H]([C@@H]1O)O)O)[NH2+][C@H]1C=C([C@H]([C@@H]([C@H]1O)O)O)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,466,P(=O)(=O)(OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O1)n1c2ncnc(c2nc1)N)O)O)OC[C@H]([C@H]([C@H](CN1C2=NC(=O)NC(=O)[C@H]2N(c2cc(c(cc12)C)C)[C@@]([NH3+])(CCc1ccccc1)c1ccccc1)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,475,c12c(c(ccc1C(=O)N)OC[P](=O)(=O)=O)c1c(C2)scn1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,478,C(c1ccc(cc1)C(F)(F)[P](=O)(=O)=O)C(Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O)(c1ccccc1)n1nnc2c1cccc2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,482,c1ccc(c(c1)OP(=O)(=O)OCC1CCCCC1)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,503,[C@@H]1(O[P](=O)(=O)=O)[C@H](O)[C@@H](O)[C@H](O)CO1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,506,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,549,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,550,C1(CC[N+](CC1)(C)C)OP(=O)(=O)OCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,551,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,552,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,583,[P](=O)(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,587,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,604,N1(C(=O)NC(=O)CC1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,632,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,644,c1ccccc1c1cc(ccc1)c1cc(ccc1)OCC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,650,O1C2N3C(CC(=O)[Fe]3([C]=O)([C]=O)/C/1=N\c1ccc3c(c1)cccc3)C(C)C(C2C)OP(=O)(=O)OC[C@H]1O[C@H]([C@H](O)[C@@H]1O)n1c2nc(N)nc(O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,657,c1ccccc1C[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,659,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,670,n1c(C)nc(N)c(c1)Cn1c(=O)sc(c1C)CCOP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,673,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,690,O=P(=O)(Oc1ccc(N(=O)=O)cc1)OCC[N+](C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,691,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,700,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,722,O=[P](=O)(=O)OC[C@H](O)C(O)(O)C(=O)CC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,725,CC(=CCC/C(=C/CC/C(=C/CC/C(=C/COP(=O)(=O)O[P](=O)(=O)=O)/C)/C)/C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,763,O=C(OC)c1ccc(cc1)[C@](n1nnc2ccccc12)(Cc1ccc(C(F)([P](=O)(=O)=O)F)cc1)C/C=C/c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,766,O=N(=O)c1ccccc1C(=O)NCC(=O)N/N=C/c1c(O)c(C)ncc1CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,773,CC(C)/C=C/C/C(=C/CC(C/C=C(\C)/C/C=C/C(C)C)([P](=O)(=O)=O)[P](=O)(=O)=O)/C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,775,[P](=O)(=O)(=O)OP(=O)(=O)O[P@@](=S)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,785,O=[P](=O)(=O)OP(=O)(=O)O/C=C/[C@@H](C)C/C=C\[C@@H](C)CNC(=O)CCCC[C@@H]1SC[C@H]2[C@@H]1NC(=O)N2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,786,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,793,[C@@H]1([C@@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,809,C(=O)(N(CC[C@@H](c1cc(Cl)c(Cl)cc1)[P](=O)(=O)=O)O)c1c(cccc1)Cn1ncnc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,821,C(OP(=O)(=O)O[P](=O)(=O)=O)CC(=C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,828,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]cnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,833,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H](O)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,847,C(C(=O)NO)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,848,[P](=O)(=O)(=O)C([P](=O)(=O)=O)(O)CC[N@H+](CCCCC)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,874,[NH3+][C@H](C(=O)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N)C(C)C)CC(=O)N)C(C)C)Cc2ccc(cc2)O[P](=O)(=O)=O)Cc2ccccc2)CCC1)CCCC[NH3+],RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,875,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](Cn1c2c(n(c3c(nc(nc13)O)O)C(=O)CCc1ccc(cc1)NC(=O)[C@H](Cc1ccccc1)NC(=O)OCc1ccccc1)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,879,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,881,C([C@@]1([C@H]([C@@H]([C@@H](CO1)O)O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,893,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,909,P(=O)(=O)(O[P](=O)(=O)=O)OCCC1=C(C)N([C@H](S1)[C@](P(=O)(=O)OC)(O)c1ccccc1)Cc1cnc(nc1N)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,911,C(=O)(c1cc(c(cc1)Br)C)N[C@H](C(=O)N[C@H](C(=O)N)CCCNC(=O)c1cccc(c1)I)Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,924,c1ccccc1C[C@@H](NC(=O)c1sc2ccccc2c1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,926,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O)O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,931,C(=O)(CO[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,933,[P](=O)(=O)(=O)OC[C@@H]1[C@H](C[C@H](N2C(=O)NC(=O)CC2)O1)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,934,C(=O)(CO[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,938,[P](=O)(=O)(=O)O[C@@H]1[C@@H]([C@@H](CO)O[C@H]1n1cnc2c(=O)[nH]c(N)nc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,950,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O)O)O[P](=O)(=O)=O)O)OP(=O)(=O)OC[C@H](COC(=O)CCC)OC(=O)CCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,953,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](O)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,962,O[C@H]1C[C@@H](O[C@@H]1CO[P](=O)(=O)=O)n1cc(CCBr)c(=O)[nH]c1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,974,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O)O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,987,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,989,[P](=O)(=O)(=O)CCCCSc1c(O)cccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1020,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1024,O=[P](=O)(=O)OCCC[C@H](C)CCC[C@@H](C)CCC[C@@H](C)CCC[C@@H](C)CCC[C@H](C)CCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1026,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1027,n1c(C)c(O)c(C)c(c1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1044,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1048,O=[P](=O)(=O)OC[C@@H](O)[C@@H](O)C(=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1059,C(=O)N(CC[C@@H](c1cc(Cl)c(Cl)cc1)[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1060,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O[P](=O)(=O)=O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OCC(C)(C)[C@@H](O)C(=O)NCCC(=O)NCC[C@@H](F)C(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1062,[P](=O)(=O)(=O)OC[C@H]1O[C@H](C[C@@H]1[NH3+])n1c(=O)[nH]c(=O)c(C)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1065,[C@H]1([C@@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)OP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1077,O=[P](=O)(=O)OC[C@@H]1[C@@H](O)[C@@H](O)[C@@H](O1)n1c(N)c(N(=O)=O)nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1084,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1102,[P](=O)(=O)(=O)OC[C@]1(O)[C@@H](O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1118,n1(c(=O)[nH]c(=O)cc1)[C@@H]1O[C@H](CO)[C@@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1137,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1167,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1170,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1180,C(=O)(OCc1ccccc1)N[C@@H](C(C)C)C(=O)N[C@@H](Cc1ccccc1)P(=O)(=O)[C@@H](NC(=O)[C@@H](NC(=O)OCc1ccccc1)C(C)C)Cc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1189,[P](=O)(=O)(=O)CP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1191,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1203,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1217,[P](=O)(=O)(=O)OC[C@H]1O[C@@H](n2c3[nH]c(=O)[nH]c(=O)c3nc2)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1225,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]cnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1242,O=[P](=O)(=O)O[C@H]1[C@H](O)[C@H]([C@@H]([C@H]([C@H]1O)O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1255,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)C[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1264,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1274,P(=O)(=O)(S)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1287,P(=O)(=O)(OP(=O)(=O)O[P](=O)(=O)=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c(=O)c(C)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1301,n1(cnc2c(N)nc(N)nc12)C[C@H](C)OC[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1312,[C@H]1([C@@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)OP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1313,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1316,CN(C(=O)C[C@H](c1ccc(cc1)C)C[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1345,n1(c(=O)nc(cc1)N)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1347,O=[P](=O)(=O)OC[C@@H]1O[C@H]([C@@H]([C@H]([C@@H]1O)O)O)S[C@H]1[C@H](O)[C@@H](O)[C@H](O)O[C@@H]1CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1360,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1361,c12cc(Cl)ccc1scc2[C@H]([P](=O)(=O)=O)C(=O)Nc1cc2ccccc2cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1364,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1373,C(SP(=O)(=O)O[P](=O)(=O)=O)/C=C(\C)/CCC[C@H](CCCC(C)C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1385,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1390,ON(CCCCO[P](=O)(=O)=O)C(=O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1392,[NH3+][C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N[C@H](C(=O)N)CC(C)C)CO)CC(C)C)C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1409,c1cc2c(cc1OCCC[P](=O)(=O)=O)c(c(n2Cc1ccccc1)C)CC(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1414,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](Cn1c2c(n(c3c(nc(nc13)O)O)C(=O)CCc1ccc(cc1)Br)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1426,FC(F)(F)C(=O)Nc1ccc(CP(=O)(=O)O[C@@H]([C@@H](CO)NC(=O)C(Cl)Cl)c2ccc(cc2)N(=O)=O)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1460,C([C@H](COP(=O)(=O)OC)OC(=O)CCCCCCCCC/C=C\CCCC)OC(=O)CCCCCCCCC/C=C\CCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1461,ON(CCCCO[P](=O)(=O)=O)C(=O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1463,n1(c(=O)[nH]c(=N)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1464,FC(F)(F)c1cccc(c1)Nc1ccccc1C(=O)N(O)CC[C@H]([P](=O)(=O)=O)c1ccc(Cl)c(Cl)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1477,[P](=O)(=O)(=O)CC(=O)OC[C@H]1O[C@@H](n2c3ncnc(N)c3nc2)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1492,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1499,P1(=O)(=O)O[C@@H]2[C@@H](COP(=O)(=O)O[P](=O)(=O)=O)O[C@@H](n3cnc4c(=O)[nH]c(N)nc34)[C@@H]2O1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1500,n1(c(=O)[nH]c(=O)c(c1)C)[C@@H]1O[C@H](COP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O)O)n2cnc3c(N)ncnc23)[C@H](C1)N=N#N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1505,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1508,n1c(C)nc(N)c(c1)CN1[CH]SC(=C1C)CCOP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1513,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1526,[P](=O)(=O)(=O)OCC(=O)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1539,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1543,[C@]1([C@@H]([C@H]([C@@H]([C@@H](CO)O1)O)O)O)(C)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1545,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O[P](=O)(=O)=O)OC(=O)C[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1552,c1cc(cc2c1cccc2)S(=O)(=O)NCCO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1557,c1c(c(ccc1)N)SCCCC[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1561,OC[C@]1(O)[C@@H](O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1563,O=[P](=O)(=O)C([P](=O)(=O)=O)Nc1ncnc2sc(cc12)c1ccc(C)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1565,c1cc(cc2c1cc(c(=O)n2O)NCc1c(cnc(c1O)C)CO[P](=O)(=O)=O)OC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1570,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1580,[P](=O)(=O)(=O)O[C@@H]1[C@@H]([C@@H](CO[P](=O)(=O)=O)O[C@H]1n1cnc2c(N)ncnc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1590,CC(=O)[C@H]([C@@H](CO[P](=O)(=O)=O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1593,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](CN1c2c(N[C@@]3(C(=O)NC(=O)N[C@@H]13)[C@H](c1ccccc1)CC(=N)CC)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1601,C(OP(=O)(=O)O[P](=O)(=O)=O)/C=C(\C)/CC/C=C(\C)/CCC=C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1604,C(c1ccc(cc1)C[C@@H](C(=O)N)NC(=O)[C@@H](NC(=O)C)Cc1ccccc1)(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1606,n1cnc2c(c1N)ncn2[C@@H]1[C@@H](O)[C@H](O[P](=O)(=O)=O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OCC(C)(C)[C@@H](O)C(=O)NCCC(=O)NCCS,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1633,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1658,n1(c(=O)nc(cc1)N)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1674,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1679,O=[P](=O)(=O)CCOCC[N@H+](CC[P](=O)(=O)=O)CCn1c2nc[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1707,n1(c(=O)[nH]c(=O)cc1)[C@@H]1O[C@H](CO)[C@@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1732,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1736,[C@H]1([C@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1737,O=[P](=O)(=O)C[C@@H](CC(=O)N(O)C)CCCCc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1744,[NH3+][C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N)C(C)C)[C@H](O)C)[C@H](O)C)C)CO)CCCC[NH3+],RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1751,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1753,c1cc2c(c(c1)O[P](=O)(=O)=O)nc(cc2)C#N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1779,O=C(c1ccccc1)[C@@H](c1ccccc1)Cc1cc(Br)c(cc1)C(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1793,[nH]1nnnc1C[C@@H]([NH3+])C(=O)N[C@@H](Cc1ccccc1)P(=O)(=O)C[C@@H](C)C(=O)N[C@@H](C)C(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1795,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1796,n1(c(=O)[nH]c(=O)c(c1)[C]1CC[C@@H](S1)CO)[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)O[C@@H]1[C@H](O)[C@@H](O)[C@@H](O)[C@H](O1)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1798,c1cc(O)ccc1C[C@@H]([P](=O)(=O)=O)NC(=O)[C@@H](NC(=O)[C@H]([C@@H](C)CC)NC(=O)C)Cc1ccc(O)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1806,c1cc2c(cc1)c(ccc2)[C@@H](O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1828,N1(c2cc(OC)c(Cc3ccccc3)cc2C/C(=N\Cc2c(CO[P](=O)(=O)=O)cnc(C)c2O)/C1=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1833,[C@@H]1(O[P](=O)(=O)=O)[C@H](O)[C@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1836,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2cnc3c(N)ncnc23)[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1837,O=[P](=O)(=O)COCC[N@@H+](CC[P](=O)(=O)=O)CCn1c2nc[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1843,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=O)cc1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1853,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1863,C(O)(CN1CC(F)CCC1)([P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1865,[P](=O)(=O)(=O)CCCCSc1cc(ccc1O)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1886,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1913,CCCCC/C=C/CC([P](=O)(=O)=O)([P](=O)(=O)=O)C/C=C/CCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1918,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1919,[C@H]1(O[C@@H]([C@H]([C@@H]1O)O)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1930,P(=O)(=O)(OP(=O)(=O)O[P](=O)(=O)=O)OC[C@H]1O[C@@H](n2c(=O)[nH]c(=O)c(C)c2)C[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1932,[C@H]1([C@@H]([C@H]([C@@H]([C@@H](CO)O1)O)O)NC(=O)C)OP(=O)(=O)OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@H](n2c(=O)[nH]c(=O)cc2)O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1941,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc(C(=O)N)n1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1942,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1954,c1cc(ccc1C[C@@H](C(=O)N[C@H]1CCCCN(C1=O)Cc1ccc(cc1)c1ccccc1)NC(=O)C)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1960,O=C(OC(C)(C)C)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CC(C)C)P(=O)(=O)CC(=O)NC[C@H](CC)C)Cc1[nH]cnc1)Cc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1962,[nH]1c(=O)[nH]c(=O)c2[nH]c3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1968,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1970,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O[P](=O)(=O)=O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1981,C(=O)(C)N[C@H](C(=O)NC1(C(=O)N[C@H](C(=O)N)CC(=O)N)CCCC1)Cc1ccc(cc1)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,1994,c1[nH]c2n(CCCCOP(=O)(=O)OP(=O)(=O)OC[C@@H]3[C@@H](O)[C@@H](O)[C@@H](O)O3)c(nc2c(=O)n1)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2002,P(=O)(=O)(OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2009,C[C@@H](OC[P@](=O)(S)OP(=O)(=O)O[P](=O)(=O)=O)Cn1cnc2c1ncnc2N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2025,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2033,P(=O)(=O)(OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O1)n1c2ncnc(c2nc1)N)O)O)OC[C@H]([C@H]([C@H](CN1[C@@H]2NC(=O)NC(=O)[C@]2(Nc2cc(c(cc12)C)C)[C@@H](CC(=N)C)c1ccccc1)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2039,O=[P](=O)(=O)C1(Cc2c(C1)cccc2)[NH3+],RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2046,O=[P](=O)(=O)CCCC[S@@](=O)c1ccccc1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2056,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2068,[NH3+][C@@H](CO[P](=O)(=O)=O)C(=O)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N)Cc2ccccc2)[C@H](O)C)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2087,CN(C)c1cccc2c1cccc2S(=O)(=O)NCCOP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1ccc(N)nc1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2091,CCCCC#CC1=CC=CN(CC([P](=O)(=O)=O)[P](=O)(=O)=O)C1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2106,O=[P](=O)(=O)OP(=O)(=O)OCC[NH+](C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2108,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2112,[C@H]1([C@@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)NC(=O)C)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2129,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=O)c(c1)Br,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2135,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2136,n1(c2nc[nH]c(=O)c2nc1)C[C@H](OCC[P](=O)(=O)=O)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2151,O=[P](=O)(=O)CC[C@@H]([NH3+])C(=O)Nc1cccc(c1)CCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2155,c1ccccc1[C@]1([P](=O)(=O)=O)O[C@H]2[C@@H](CO1)O[C@H](C2)n1cc(c(=O)[nH]c1=O)/C=C\[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2158,O=[P](=O)(=O)c1ccccc1OCC(=O)Nc1cc(ccc1)OC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2187,c1c(Cl)c(Cl)ccc1[C@@H]([P](=O)(=O)=O)CCN(O)C(=O)c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2191,CCCCCCCCCCCCCCCC(=O)O[C@H](COP(=O)(=O)O[C@@H]1[C@H](O)[C@H](O[P](=O)(=O)=O)[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O)COC(=O)CCCCCCCCCCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2194,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)OP(=O)(=O)O[C@@H]1[C@@H]([C@H]([C@@H]([C@@H](CO)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2195,C(=C\CCCCCCCC)\CCCCCCCC(=O)SCCNC(=O)CCNC(=O)[C@H](O)C(C)(C)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@H](O)[C@@H]1O[P](=O)(=O)=O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2197,[P](=O)(=O)(=O)OP(=O)(=O)CP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2198,c1(ccc(cc1)C(F)(F)[P](=O)(=O)=O)C[C@@H](C(=O)N[C@@H]1C(=O)N(CCCC1)Cc1ccc(cc1)c1ccccc1)NC(=O)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2199,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2206,O=[P](=O)(=O)c1ccccc1OCC(=O)Nc1c(Cl)c(Cl)ccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2216,C(=O)(OCc1ccccc1)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CC(C)C)P(=O)(=O)O[C@@H](Cc1ccccc1)C(=O)OC)C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2217,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2218,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2227,CNc1ccccc1C(=O)O[C@H]1[C@@H](O)[C@@H](O[C@@H]1COP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O)n1cnc2c1nc(N)[nH]c2=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2229,[P](=O)(=O)(=O)OCC(=O)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2231,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2237,c1ccc(c(c1)OP(=O)(=O)OCc1ccccc1)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2241,O=[P](=O)(=O)C([P](=O)(=O)=O)(O)Cn1cncc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2242,O=[P](=O)(=O)C([P](=O)(=O)=O)Nc1ncnc2sc(cc12)c1ccc(cc1)[C@H]1C=C1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2262,C(OP(=O)(=O)O[P](=O)(=O)=O)/C(=C(\C)/CC/C=C(\C)/CCC=C(C)C)/F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2288,n1(cnc2c(N)ncnc12)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)OP(=O)(=O)O[C@H]1[C@@H]([C@H]([C@@H]([C@@H]([C@H](CO)O)O1)O)O)F)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2290,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2297,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2307,N#CCC[N@@H+](CC[P](=O)(=O)=O)CCn1c2nc(N)[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2310,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2312,[C@@H]1(O[P](=O)(=O)=O)[C@H](O)[C@H](O[P](=O)(=O)=O)[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2318,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2324,c1ccccc1Oc1ccc2c(c1)cc(c(=O)n2O)NCc1c(cnc(C)c1O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2328,[NH3+][C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C(=O)N[C@H](C(=O)N1[C@H](C(=O)N[C@H](CO)CC(=O)N)CCC1)[C@H](O)C)[C@H](O)C)CO)C)CCC(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2340,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2352,C(=O)(CO[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2362,[C@@H]1([C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@@H](O)[C@@H](O)[C@@H](O)CN1[C@@H]2NC(=O)NC(=O)[C@]32N(c2c1cc(c(c2)C)C)[C@@H](C[C@@H]3c1c(F)c(F)c(F)c(c1F)F)O)n1c2nc[nH]c(=N)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2400,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2403,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2410,[P](=O)(=O)(=O)OC[C@H]1O[C@@H](n2c(O)nc(=O)cc2O)[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2421,C(=O)(C)N[C@H](C(=O)N[C@H](C(=O)N)CCCNC(=O)c1cccc(c1)I)Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2430,[C@]1(CO)([C@H]([C@@H]([C@H](O1)CO)OP(=O)(=O)O[C@@H]1[C@@H](O)[C@@H](O)CO[C@H]1O)OC(=O)c1ccccc1)O[C@@H]1[C@H](O)[C@H](O)[C@@H](O)[C@@H](CO)O1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2432,[P](=O)(=O)(=O)C([P](=O)(=O)=O)(O)CC[NH3+],RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2442,n1cnc2c(c1N)ncn2[C@H]1[C@H](O[C@@H]2[C@H](O)[C@H](O)[C@H](O2)CO[P](=O)(=O)=O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2448,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2452,O=[P](=O)(=O)CCOCCn1cnc2c1nc[nH]c2=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2455,[nH]1c(=O)[nH]c(=O)c2[nH]c3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2458,c1cc(cc(c1F)F)[C@@H]([P](=O)(=O)=O)CCC(=O)N(C)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2495,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2498,O=[P](=O)(=O)[C@@H]([NH3+])CC1CCCCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2504,O=[P](=O)(=O)[C@@H]([NH3+])CCC1CCCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2505,P(=O)(=O)(OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2516,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2522,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2530,O=[P](=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=N)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2544,c1c(cc(c(c1CO)O[P](=O)(=O)=O)CO)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2547,[C@@H]1([C@@H]([C@H]([C@@H]([C@H](O1)CO[P](=O)(=O)=O)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2577,c1cc(CO)ccc1C(=O)NCCC(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N)CO)Cc1n(CCCCCCCCc2ccccc2)cnc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2584,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2585,S(=O)(=O)(O)OP(=O)(=O)OC[C@H]1O[C@@H](n2c3ncnc(N)c3nc2)[C@H](O)[C@@H]1O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2607,O=[P](=O)(=O)[C@H](F)C(=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2608,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2612,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1CC(C(=O)N)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2613,OC[C@H]1O[C@@H](n2ccc(=O)[nH]c2=O)[C@H](O[P](=O)(=O)=O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2615,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2620,c1ccccc1c1ccc(cc1)c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2626,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H](C[C@@H]1O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2627,O=[P](=O)(=O)CCOCC[N@@H+](CC[P](=O)(=O)=O)CCn1c2nc(N)[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2635,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2639,O=[P](=O)(=O)OC[C@@H]1[C@@H](O)[C@@H](O)[C@@H](O1)N1[C@H](CC(=O)NC1=O)C#N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2671,N(CCCO[P](=O)(=O)=O)(O)C(=O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2678,P(=O)(=O)(OC(=O)c1cccc(c1)c1noc(n1)c1ccccc1F)OC[C@@H]1[C@H]([C@H]([C@H](n2c3ncnc(c3nc2)N)O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2690,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2693,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OCCCCC[C@@H]1SC[C@@H]2NC(=O)N[C@H]12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2701,c1cccc(c1)[C@@H]([P](=O)(=O)=O)[NH2+]Cc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2711,[P](=O)(=O)(=O)OP(=O)(=O)O[P@](=O)(S)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2731,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2747,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2751,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2755,[C@@H]1([C@H]([C@H]([C@@H]([C@H]([C@@H]1O[P](=O)(=O)=O)OC(=O)C[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2768,P(=O)(=O)(OP(=O)(=O)O[P](=O)(=O)=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c(=O)c(C)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2773,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2782,[C@H](C(O)O)(CP(=O)(=O)[C@@H]([NH3+])CCc1ccccc1)Cc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2783,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@H]2[C@@H]1O[C@@]1(O2)C(=CC(=C[C@H]1N(=O)=O)N(=O)=O)N(=O)=O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2787,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2791,C(=O)(CCCNC(=O)c1ccccc1)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@H](C)O[P](=O)(=O)=O)C(=O)N)CO)Cc1n(CCCCCCCCc2ccccc2)cnc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2793,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2803,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2807,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2827,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2839,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2855,O=[P](=O)(=O)O[C@H]1C[C@@H]([C@@H]2[C@@]1(CO[P](=O)(=O)=O)C2)n1cnc2c1nc(I)nc2NC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2867,[P](=O)(=O)(=O)/C=C/CCSc1c(O)cccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2880,[P](=O)(=O)(=O)OC/C=C/O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2884,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1[NH2+]C[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2887,C(=C\NC(=O)[C@@H](P(=O)(=O)C)c1csc2c1cc(cc2)Cl)/c1ccc(F)c(F)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2897,O=[P](=O)(=O)[C@@H]([NH3+])c1ccc(cc1)n1nccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2900,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O)O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2901,c1ccccc1CO[C@@H]1[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@@H]([C@@H]([C@H]1O[P](=O)(=O)=O)O[P](=O)(=O)=O)OCc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2904,C1O[C@@H]([C@H]([C@@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2910,[P](=O)(=O)(=O)OC[C@]1(O)[C@@H](O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2912,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2925,[P](=O)(=O)(=O)OP(=O)(=O)CP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2926,[P](=O)(=O)(=O)O[C@H]1[C@@H]([C@@H](CO)O[C@H]1n1cnc2c(=O)[nH]c(N)nc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2946,Oc1c2ccccc2ccc1C(=O)SCCNC(=O)CCNC(=O)[C@H](O)C(C)(C)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O[P](=O)(=O)=O)O)n1cnc2c1ncnc2N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2964,P(=O)(=O)(O[C@@H]1[C@H](O)[C@H](O)[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O)OC[C@H](OC(=O)CCC)COC(=O)CCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2966,O=[P](=O)(=O)CC(=O)O[C@@H]1[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@@H]([C@@H]([C@H]1O[P](=O)(=O)=O)O[P](=O)(=O)=O)OCc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2970,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O[P](=O)(=O)=O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OCC(C)(C)[C@@H](O)C(=O)NCCC(=O)NCCCC(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2972,[P](=O)(=O)(=O)CC[C@@H](CO)[NH2+]Cc1c2nc[nH]c(=O)c2[nH]c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2994,[NH3+][C@@H](CO[P](=O)(=O)=O)C=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,2996,O=[P](=O)(=O)CC(=O)NCCNC(=O)C[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3014,[NH3+][C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C(=O)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N2[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C(=O)N3[C@H](C=O)CCC3)Cc3ccc(cc3)O)CO)CCC2)CO)[C@H](O)C)CCC1)Cc1ccc(cc1)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3017,c1(ccc(cc1)C(=O)NCCO[P](=O)(=O)=O)OC(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3034,Fc1c(=O)[nH]c(=O)n(c1)[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3036,c1ccc(cc1)[C@@](n1c2c(cccc2)nn1)(Cc1ccc(cc1)c1cc2ccc(nc2c(c1)[P](=O)(=O)=O)[C@@H](OC)CC(C)C)Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3040,O=[P](=O)(=O)OC[C@@H](O)COC(=O)CCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3048,[nH]1c(=O)[nH]c(=O)c2[nH]c3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3053,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1C(=O)NC(=O)CC1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3058,n1(cc(c(=O)[nH]c1=O)C)[C@@H]1O[C@H](COP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]2O[C@H]([C@H](O)[C@@H]2O)n2c3ncnc(N)c3nc2)[C@H](C1)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3097,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3107,O[C@@H]1[C@H](O)[C@@H](O)[C@@H](CO)O[C@@H]1O[C@@]1(CO)[C@@H](O)[C@@H]([C@H](O1)CO)OP(=O)(=O)O[C@@H]1[C@@H](O)[C@@H](O)CO[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3113,O=C[C@@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3117,O=[P](=O)(=O)OP(=O)(=O)OC/C=C(/CO)\C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3120,[C@@H]1(O[P](=O)(=O)=O)[C@H](O)[C@H](O[P](=O)(=O)=O)[C@@H](O)[C@H](O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3130,c1(c(nc(c(c1CO)CO[P](=O)(=O)=O)N)C)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3132,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3139,c1ccccc1[C@]1([P](=O)(=O)=O)O[C@H]2[C@@H](CO1)O[C@H](C2)n1cc(c(=O)[nH]c1=O)/C=C/[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3140,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3143,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H](C[C@@H]1OC(=O)c1c(N)cccc1)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3146,c1cc(ncc1)NCC([P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3151,ON(CCCCO)C(=O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3160,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3161,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3162,O=[P](=O)(=O)Oc1c(O[P](=O)(=O)=O)c(O[P](=O)(=O)=O)ccc1O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3166,O=c1ccn(c(=O)[nH]1)[C@H]1C[C@H](O)[C@H](O1)COP(=O)(=O)NP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3171,n1(c(=O)[nH]c(=O)cn1)[C@H]1[C@@H]([C@@H]([C@H](O1)CO[P](=O)(=O)=O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3185,O=[P](=O)(=O)C([P](=O)(=O)=O)(O)Cn1cncc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3191,O=[P](=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1C=C(C(=O)N)C=CC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3192,P(=O)(=O)(OP(=O)(=O)O[P](=O)(=O)=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c(=O)c(C)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3195,O([C@H]1[C@H]([C@@H](O[C@@H]1COP(=O)(=O)OP(=O)(=O)OCC(C)(C)[C@H](C(=O)NCCC(=O)NCCCC(=O)C)O)n1c2c(c(N)ncn2)nc1)O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3196,[P](=O)(=O)(=O)CP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3200,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1[NH2+]C[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3209,C(=O)(C)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N)CO)Cc2nc[nH]c2)CC(C)C)C[C@H](C1)OCCCCc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3223,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3247,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O[P](=O)(=O)=O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OCC(C)(C)[C@@H](O)C(=O)NCCC(=O)NCCSC(=O)CC(=O)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3250,O=C(N1CCC(N(C(=O)c2c(C(=O)[C@H]([P](=O)(=O)=O)c3cccc4c3cccc4)cc3ccccc3c2)C)CC1)c1ccc2c(c1)cccc2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3254,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3255,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3256,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3260,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3264,C(OP(=O)(=O)O[P](=O)(=O)=O)C=C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3269,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@H]2[C@@H]1O[C@]1(O2)C(=CC(=C[C@H]1N(=O)=O)N(=O)=O)N(=O)=O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3275,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2nc(C(=O)N)nc2)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3284,O=[P](=O)(=O)OC[C@H]1O[C@H]([C@H](OP(=O)(=O)OC[C@H]2O[C@@H](n3cnc4c3ncnc4N)[C@H](O[P@@H](=O)OC[C@H]3O[C@@H](n4cnc5c4ncnc5N)[C@H](O)[C@@H]3O)[C@@H]2O)[C@@H]1O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3286,O=[P](=O)(=O)OP(=O)(=O)OC/C=C(\C)/C[NH3+],RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3287,C(C(=O)NO)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3290,[P](=O)(=O)(=O)OP(=O)(=O)CP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3295,O[C@](CBr)(C)CCOP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3303,C(=O)(c1cc(c(cc1)Br)C)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](C(=O)N)CNC(=O)Cc1cc(c(cc1)O)OC)CCCNC(=O)c1cccc(c1)I)Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3331,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)c1[nH]nc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3356,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3357,[C@H]12[C@@H](OC(=O)N[C@H]([C@@H](CN(CC(C)C)S(=O)(=O)c3ccc(OC)cc3)O)Cc3ccc(OC[P](=O)(=O)=O)cc3)CO[C@H]1OCC2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3390,C(=O)(C)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N)CO)Cc2n(CCCCCCCCc3ccccc3)cnc2)CC(C)C)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3415,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)COP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3431,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3436,O=[P](=O)(=O)c1cc(Br)ccc1OCc1cc(ccc1)OCCCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3438,O=C(N(O)C)C[C@H](C[P](=O)(=O)=O)CCCc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3440,[P](=O)(=O)(=O)OC[C@H]1[NH2+][C@H]([C@@H]([C@@H]1O)O)c1c[nH]c2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3448,c1(ccc2c(c1)c1c(sc(n1)N)CC2)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3453,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3463,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O[P](=O)(=O)=O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1OC[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3466,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3469,O=[P](=O)(=O)C([P](=O)(=O)=O)Nc1cc(ccn1)c1ccc(cc1)OC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3477,O=[P](=O)(=O)c1ccccc1OCC(=O)Nc1cc(C(F)(F)F)ccc1Cl,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3503,O=[P](=O)(=O)OC[C@@H]1[C@@H](O)[C@@H](O)[C@@H](O1)N1[C@H](CC(=O)NC1=O)C#N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3508,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3519,O([C@H]1O[C@H]([C@H](C1)O)n1cnc2c1ncnc2NP(=O)(=O)O[C@@H]1[C@@H](O)[C@H](O)[C@@H](CO)O[C@@H]1O)P(=O)(=O)NC(=O)[C@@H]([C@H](C(C)C)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3521,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc(C(=O)N)n1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3524,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3529,O=[P](=O)(=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3544,P(=O)(=O)(OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1CN(c2c(=O)[nH]c(N)nc12)C)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3547,C(=O)(CCc1ccccc1)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N[C@H](C(=O)N)C)CO)Cc2nc[nH]c2)CC(C)C)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3571,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3582,Fc1c(=O)[nH]c(=O)n(c1)[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3588,[C@H]1([C@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3599,[nH]1c(=O)[nH]c(=O)c2[nH]c3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3602,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3603,[P](=O)(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@H](c2c(O)c([nH]n2)C(=O)N)O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3613,P(=O)(=O)(S)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3615,P(=O)(=O)(O[P](=O)(=O)=O)OCC[C@@H]1[C@@H](C)[N@@H+]([C@H](S1)[C@](P(=O)(=O)OC)(O)C1CCCCC1)C[C@H]1C[NH2+][C@H]([NH2+][C@@H]1[NH3+])C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3628,O1C2N3[Fe]([C]=O)([C]=O)(C(=O)CC3C(C)C(C2C)OP(=O)(=O)OC[C@H]2O[C@H]([C@H](O)[C@@H]2O)n2c3nc(N)[nH]c(=O)c3nc2)/C/1=N/CS(=O)(=O)Cc1ccc(C)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3635,Nc1ncnc2n(cnc12)[C@@H]1O[C@H](COP(=O)(=O)OP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O)[C@@H](O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3636,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3641,Fc1c(=O)[nH]c(=O)n(c1)[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3651,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3660,[P](=O)(=O)(=O)COc1c2c(ccc1)Cc1c2ncs1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3665,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2cnc3c(N)ncnc23)[C@H]2[C@@H]1O[C@]1(C(=CC(=C[C@@H]1N(=O)=O)N(=O)=O)N(=O)=O)O2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3678,[P](=O)(=O)(=O)[C@@H](C(=O)c1cc2ccccc2cc1)c1cccc2c1cccc2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3692,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2cnc3c(N)ncnc23)[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3697,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O[P](=O)(=O)=O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OCC(C)(C)[C@@H](O)C(=O)NCCC(=O)NCCS,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3711,O=[P](=O)(=O)C[C@H](CC(=O)N(O)C)c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3714,[P](=O)(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@H](n2c(=O)[nH]c(=O)c(c2N)F)O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3725,n1(c(=O)[nH]c(=N)cc1)[C@H]1[C@H](O[P](=O)(=O)=O)[C@@H]([C@H](O1)CO)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3733,O=[P](=O)(=O)OC[C@@H]1[C@H]([C@@H](O)[C@@H](O1)N1[C@@H](C(=O)C)CC(=O)NC1=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3737,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3738,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](Cn1c2c(n(c3c(=O)[nH]c(=O)[nH]c13)C(=O)CCc1ccccc1)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3746,c1ccccc1c1cn(c(=O)[nH]c1=O)[C@@H]1O[C@@H]([C@H]([C@H]1O)O)COP(=O)(=O)OP(=O)(=O)O[C@H]1O[C@H](CO)[C@H](O)[C@H](O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3749,O=[P](=O)(=O)C([P](=O)(=O)=O)Nc1ncnc2sc(cc12)c1ccc(C)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3756,OC[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3766,c1ccccc1c1cc(ccc1)c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3769,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3777,[C@@H]1(OP(=O)(=O)OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O)O)n2c(=O)[nH]c(=O)cc2)[C@H](F)[C@@H](O)[C@H](O)[C@@H](CO)O1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3783,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3793,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3794,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3805,C1(=O)NC(=O)N[C@@]21[C@H](O)[C@H](O)[C@H](O2)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3810,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3814,n1(c(=O)[nH]c(=O)cc1)[C@@H]1O[C@H](CO)[C@@H](O[P](=O)(=O)=O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3825,n1(cc(c(=O)[nH]c1=O)C)[C@@H]1O[C@H](COP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]2O[C@H]([C@H](O)[C@@H]2O)n2c3ncnc(N)c3nc2)[C@H](C1)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3833,O=[P](=O)(=O)OCc1cnc(C)c(O)c1/C=N/NC(=O)CNC(=O)c1c(C(F)(F)F)cccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3843,C(C(=O)NO)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3847,P(=O)(=O)(O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3851,CCCCCC(=O)OC[C@@H](OC(=O)CCCCC)CCP(=O)(=O)OCC[N+](C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3853,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3861,C(C(=O)[C@H]([C@@H](CO[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3863,n1(c(=O)nc(cc1)N)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3866,O=[P](=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=N)c(O)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3869,[C@H]1([C@@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3876,C(OP(=O)(=O)O[P](=O)(=O)=O)/C=C(\C)/CCC=C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3882,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3890,O=P1(=O)OP(=O)(=O)OCC2=C([C@H]([C@H](n3cnc4c(=O)n([C@H]5[C@@H]([C@@H]([C@@H](CO1)O5)O)O)cnc34)O2)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3896,[P](=O)(=O)(=O)N[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3923,c1c2ccccc2ccc1c1ncnc2c1ncn2[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3933,C(=O)(C[C@@]12C[C@@H]3C[C@@H](C[C@H](C1)C3)C2)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N[C@H](C(=O)N)CCSC)CO)Cc2nc[nH]c2)CC(C)C)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3939,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3955,[P](=O)(=O)(=O)CP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3958,c1ccc2cc(C(=O)Nc3ccc(cc3)Cl)c(cc2c1)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3969,C(C[NH3+])O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3975,O=C(OC[C@@H](O)COP(=O)(=O)OC)CCCCCCCCCCCCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3979,c1cn(c(=O)[nH]c1=O)[C@H]1[C@@H]([C@@H]([C@@H](COP(=O)(=O)OP(=O)(=O)O[C@@H]2[C@@H]([C@H]([C@@H]([C@@H](CO)O2)O)OC(=O)C[C@@H](CCCCCCCCCCC)O)[NH3+])O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3983,O=[P](=O)(=O)[C@@H]([NH3+])c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3985,n1(c(=O)[nH]c(=O)c(c1)F)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3993,C/C(=C\COP(=O)(=O)O[P](=O)(=O)=O)/CC/C=C(\C)/COCc1cccc(c1)C(=O)c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,3996,O=[P](=O)(=O)OC[C@@H](O)[C@@H](O)C(=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4007,O=[P](=O)(=O)OCCCCCCn1c2c(=O)[nH]c(=O)[nH]c2n(c1=O)C[C@@H](O)[C@H](O)[C@H](O)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4010,[NH3+]CCCC[C@H]([NH3+])C(=O)NOP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c1ncnc2N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4012,C1CCCCC1NC(=O)COc1ccccc1[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4019,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=O)cc1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4024,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4026,C(c1ccc2ccc(cc2c1)C(F)(F)[P](=O)(=O)=O)(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4033,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O[P](=O)(=O)=O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OCC(C)(C)[C@@H](O)C(=O)NCCC(=O)NCCS,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4036,[P](=O)(=O)(=O)OP(=O)(=O)OCCC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4043,C(=O)(C)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N)CO)Cc1n(CCCCCCCCc2ccccc2)cnc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4055,[NH2+]1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C(=O)N2[C@H](C(=O)N[C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C(=O)N3[C@H](C=O)CCC3)[C@H](O)C)CCC2)Cc2ccc(cc2)O)CO)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4072,O=C([C@H](Cc1onc(c1)c1ccccc1)CP(=O)(=O)c1ccc(Br)cc1)N[C@H](C(=O)N[C@H](C(=O)N)C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4075,P(=O)(=O)(O[C@H]1[C@H](O)[C@@H](O)[C@H](O)[C@@H](O)[C@H]1O)OC[C@H](O)COC(=O)CCCCCCCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4083,[P](=O)(=O)(=O)C([P](=O)(=O)=O)(O)CC[N@@H+](CCCCC)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4093,O=C1NC(=O)N[C@H]2[C@@]31N(c1cc(C)c(C)cc1N2C[C@H](O)[C@H](O)[C@H](O)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c1ncnc2N)[C@@H](C[C@@H]3c1ccccc1)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4101,CC(C)OP(=O)(=O)O[C@@H]1[C@@H](O)[C@@H](O)CO[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4109,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4113,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2cnc3c(N)ncnc23)[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4115,O=[P](=O)(=O)COCC(COC[P](=O)(=O)=O)Cn1cnc2c1nc(N)[nH]c2=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4136,O=[P](=O)(=O)C([P](=O)(=O)=O)Nc1ncccc1C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4141,c1(ccc(cc1)OC)c1ccc(cc1)S(=O)(=O)N[C@H](C(C)C)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4148,O=[P](=O)(=O)C(F)(F)CCCCn1c2c(=O)[nH]c(=O)[nH]c2n(c1=O)C[C@@H](O)[C@H](O)[C@H](O)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4154,O=[P](=O)(=O)C([P](=O)(=O)=O)Cc1c([C@@H](C)C(C)C)[nH]c2ccc(cc12)c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4158,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4165,c1(c(O[P](=O)(=O)=O)cc(O[P](=O)(=O)=O)cc1O[P](=O)(=O)=O)c1cc(O[P](=O)(=O)=O)cc(O[P](=O)(=O)=O)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4178,C(COP(=O)(=O)OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@H](n2c3ncnc(c3nc2)N)O1)O)O[P](=O)(=O)=O)(C)(C)[C@@H](O)C(=O)NCCC(=O)NCCNC(=O)Cc1cc(cc(c1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4207,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4214,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4227,n1cnc2c(c1N)ncn2[C@H]1[C@H](O[C@@H]2[C@H](O)[C@H](O)[C@H](O2)CO[P](=O)(=O)=O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4233,P(=O)(=O)(OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4244,c1ccc(c(c1CO)O[P](=O)(=O)=O)OC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4253,[P](=O)(=O)(=O)N[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4256,c1cccc(c1CO)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4280,O=[P](=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=O)c(c1)C#N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4285,O=[P](=O)(=O)OCc1cnc(C)c(O)c1C/N=N/c1nc2ccccc2s1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4299,O=[P](=O)(=O)C([P](=O)(=O)=O)C[NH2+]CCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4301,[P](=O)(=O)(=O)O[C@@H]1[C@@H](CO)O[C@@H]([C@H]1O)n1c(=O)[nH]c(=N)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4312,C(c1cc(ccc1B1O[C@@H]2[C@@H](CO[P](=O)(=O)=O)O[C@@H](n3cnc4c(N)ncnc34)[C@@H]2O1)F)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4315,N([C@@H](C(C)C)C(=O)N[C@H]1CC(=O)NCc2cc(C[C@H](OP(=O)(=O)[C@@H](NC1=O)CC(C)C)C(=O)OC)ccc2)C(=O)CC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4327,[P](=O)(=O)(=O)O[C@@H]1[C@@H]([C@@H](CO[P](=O)(=O)=O)O[C@H]1n1cnc2c(N)ncnc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4333,c1c2ccccc2ccc1S(=O)(=O)Nc1cc(ccc1)c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4340,c12c(c(=O)[nH]c(N)n1)ncn2C[C@H](OCC[P](=O)(=O)=O)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4345,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4347,c12c(c(ncn1)N)ncn2[C@@H]1O[C@H](COP(=O)(=O)OP(=O)(=O)OCC([C@H](C(=O)NCCC(=O)NCCSC(=O)CC[C@@H]2C(=O)CC[C@]3(C)C(=O)CC[C@@H]23)O)(C)C)[C@@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4376,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4382,[NH3+][C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)O)CCCC[NH3+])CCC(=O)N)C(C)C)CCC(=O)N)C)Cc1ccc(cc1)O[P](=O)(=O)=O)[C@H](CC)C)[C@H](O)C)CC(C)C)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4384,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4388,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4392,S(CCNC(=O)CCNC(=O)[C@H](O)C(COP(=O)(=O)OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O1)n1c2ncnc(c2nc1)N)O)O[P](=O)(=O)=O)(C)C)CC(=O)CCCCCCCCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4415,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4422,O=[P](=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4425,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4434,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4450,C(=C(\C)/CCC[C@H](C)COC(=O)C(=N#N)C(F)(F)F)\COP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4453,P(=O)(=O)(S)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4454,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4457,O=S1(=O)NC(=O)c2ncn(c2N1)[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4479,[NH3+]CC[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4481,O=[P](=O)(=O)OC[C@@H]1[C@@H](O)[C@@H](O)[C@@H](O1)n1c(CC)cc(=O)[nH]c1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4491,O=[P](=O)(=O)CCOCC[N@H+](CC[P](=O)(=O)=O)CCn1c2nc(N)[nH]c(=O)c2nc1Br,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4504,C(O)[C@@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4508,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O[P](=O)(=O)=O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1C=C(C(=O)N)CC=C1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4527,c1cc(O)ccc1C[C@@H]([P](=O)(=O)=O)NC(=O)[C@@H](NC(=O)[C@H]([C@@H](C)CC)NC(=O)C)Cc1ccc(O)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4542,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4549,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2cnc3c(N)ncnc23)[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4559,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)CP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)c1nc(cs1)C(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4563,OC[C@]1(O)[C@@H](O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4564,C(=O)(C)N[C@H](C(=O)NC1(C(=O)N[C@H](C(=O)N)CC(=O)N)CCC1)Cc1ccc(cc1)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4566,N([C@@H](CO[P](=O)(=O)=O)Cc1cccc(c1)C)C(=O)c1cc2c(s1)cccc2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4569,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4595,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4637,O=P(=O)([C@@H]([NH3+])CCc1ccccc1)C[C@H](C(=O)N[C@H](C(=O)N)Cc1c[nH]c2c1cccc2)CC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4642,P(=O)(=O)(S)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4655,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H](C1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4683,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4687,O=c1cc(S)n(c(=O)[nH]1)[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4688,C(=O)(CO[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4699,O=[P](=O)(=O)c1ccccc1OCC(=O)Nc1cc(Cl)ccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4701,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4735,CO[C@H](CO[P](=O)(=O)=O)[C@@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4745,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4762,CNP(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@H](O)[C@@H]1O)n1c2nc(N)[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4767,[P](=O)(=O)(=O)CP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4771,O=C(OC(C)(C)C)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CC(C)C)P(=O)(=O)CC(=O)NC[C@H](CC)C)Cc1[nH]cnc1)Cc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4774,[P](=O)(=O)(=O)O[C@@H]1[C@@H](CO[P](=O)(=O)=O)O[C@H]([C@@H]1O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4782,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)OP(=O)(=O)O[C@@H]1[C@@H]([C@H]([C@H]([C@@H](CO)O1)O)O)F)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4789,[NH2+]1[C@H](C(=O)N2[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N[C@H](C(=O)N)C)CO)Cc3nc[nH]c3)CC(C)C)CCC2)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4805,C(c1ccc(cc1)C(F)(F)[P](=O)(=O)=O)C(Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O)(c1cc(c(cc1)F)F)n1nnc2c1cccc2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4833,O=[P](=O)(=O)C([P](=O)(=O)=O)(O)Cc1cc(ccc1)c1cc(ccc1)NS(=O)(=O)c1cc(ccc1)S(=O)(=O)Nc1cc(ccc1)c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4844,N1(C(=O)NC(=O)CC1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4855,c1(ccc(cc1)C(=O)NCCO[P](=O)(=O)=O)OC(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4864,n1(cnc2c(N)nc(N)nc12)C[C@H](C)OC[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4865,c1c(ccc2c1cc(c1n2c(=O)[nH]n1)NCc1c(cnc(c1O)C)CO[P](=O)(=O)=O)Oc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4866,[C@@H]1(O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4869,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4873,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4878,[P](=O)(=O)(=O)OP(=O)(=O)CP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4881,C(=O)(CO[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4883,O=[P](=O)(=O)[C@@H]([NH3+])[C@@H](C)CCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4923,O=[P](=O)(=O)C([P](=O)(=O)=O)C[NH2+]CCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4925,C(c1ccccc1)C[C@@H](P(=O)(=O)C[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CCCC[NH3+])C(=O)N)Cc1ccccc1)CO)Cc1ccccc1)C)Cc1nc[nH]c1)Cc1nc[nH]c1)CCCC[NH3+])CC(C)C)[NH3+],RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4933,c12ccccc1[nH]cc2C[C@@H](C(=O)N)NC(=O)[C@@H](CP(=O)(=O)c1ccccc1)Cc1cc(no1)c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4939,O=C(c1ccc(C)cc1)N[C@H]1[C@@H](O)[C@@H](CO)O[C@@H](SC)[C@@H]1OP(=O)(=O)OCc1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4946,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](Cn1c2c(n(c3c(nc(nc13)O)O)C(=O)CCc1ccc(cc1)Br)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4951,[C@@H](CC(C)C)([NH3+])[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4952,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4972,F[C@@]12[C@@H](O)C[C@@]3(C)[C@H]([C@@H]2CCC2=CC(=O)C=C[C@]12C)C[C@@H](C)[C@]3(O)C(=O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,4997,OC[C@@H](O)COP(=O)(=O)O[C@@H]1[C@H](O)[C@H](O[P](=O)(=O)=O)[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5019,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]cnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5023,O(P(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)P(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1CC(C(=O)N)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5045,O=c1ccn(c(=O)[nH]1)[C@H]1C[C@H](O)[C@H](O1)COP(=O)(=O)C[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5051,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5056,O=[P](=O)(=O)C(Nc1ncc(Cl)cc1)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5062,c1(cccc2c1c1c(C2)sc(n1)N)OC[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5063,[C@@H](CC(C)C)([NH3+])[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5066,O=C(Cc1cccc2c1cccc2)N[C@H](C(=O)NCP(=O)(=O)O[C@@H](Cc1ccccc1)C(=O)OC)C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5077,O=[P](=O)(=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5088,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5092,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1CC(C(=O)N)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5103,O=[P](=O)(=O)O[C@H]1[C@@H](O[P](=O)(=O)=O)[C@@H]([C@@H]([C@H]([C@@H]1O[P](=O)(=O)=O)O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5112,CCCCCCCCCCCCP(C)(C)CC([P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5117,C1CCCC1NC(=O)COc1ccccc1[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5118,C(C(=O)NO)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5141,O=[P](=O)(=O)OC[C@@H](O)[C@@H](O)C(=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5152,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1CC(C(=O)N)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5156,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H](C1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5160,CC(=CCC/C(=C/CC/C(=C/CONC(=O)C[P](=O)(=O)=O)/C)/C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5164,O=c1[nH]c(=N)ccn1[C@@H]1O[C@@H]2COP(=O)(=O)O[C@H]2[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5166,[C@@H](CC(C)C)([NH3+])[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5169,c1c2ccccc2ccc1S(=O)(=O)Nc1cc(ccc1)c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5174,O=[P](=O)(=O)C([P](=O)(=O)=O)(O)CCc1cccnc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5176,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H](O)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5179,P(=O)(=O)(NC(C)C)NC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5201,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)S[C@@H]1/C(=N\C(=O)C)/[C@@H](O)[C@H](O)[C@H](O1)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5205,NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5209,P(=O)(=O)(S)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5215,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5216,O=[P](=O)(=O)O[C@@H]1[C@H](NC(=O)C[C@H](O)CCCCCCCCCCC)[C@H]([C@@H]([C@H](O1)CO)O)OC(=O)C[C@H](O)CCCCCCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5220,[nH]1c(=O)[nH]c(Cl)c(CCCCO[P](=O)(=O)=O)c1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5224,O(P(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5228,c1cccc(c1O)N[C@@H]([C@@H](CO[P](=O)(=O)=O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5229,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5232,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5236,[C@@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O)O)(O)C(=O)N=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5237,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5248,[N+](CCOP(=O)(=O)OC[C@H](CO)OC(=O)CCCCC)(C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5258,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5262,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5272,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5274,CCCCCCCCC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5278,n1(c(=O)[nH]c(=N)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5316,P(=O)(=O)(OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O1)n1c2ncnc(c2nc1)N)O)O)OC[C@H]([C@H]([C@H](CN1[C@@H]2NC(=O)NC(=O)[C@]2(Nc2cc(c(cc12)C)C)[C@@H](CC(=N)Cc1ccccc1)c1ccccc1)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5319,C(O)c1sc(cc1)c1cn(c(=O)[nH]c1=O)[C@@H]1O[C@@H]([C@H]([C@H]1O)O)COP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5357,P(=O)(=O)(OP(=O)(=O)O[P](=O)(=O)=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c(=O)c(C)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5359,[C@@H]1(O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5362,O=P(=O)(OCC[N+](C)(C)C)OC[C@@H](COC(=O)CCCCCCC)OC(=O)CCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5374,C(O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5383,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5384,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5393,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)COCCn1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5407,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5415,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5416,P(=O)(=O)(O[P](=O)(=O)=O)OCC[C@H]1[C@H](C)[N@@H+]([C@H](S1)[C@](P(=O)(=O)OC)(O)C1CCCCC1)CC1=CN[C@@H]([NH2+][C@@H]1[NH3+])C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5421,[C@H](/N=C/c1c(c(ncc1CO[P](=O)(=O)=O)C)O)([P](=O)(=O)=O)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5425,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5439,O=[P](=O)(=O)OP(=O)(=O)OCC#C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5441,OCc1c(ccc(c1)C[C@@H](C(=O)N[C@H]1CCCCN(C1=O)Cc1ccc(cc1)c1ccccc1)NC(=O)C)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5449,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5454,O=[P](=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=O)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5458,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@H]([C@@H]1O)O)n1c(=O)[nH]c(=N)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5464,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5465,P(=O)(=O)(OC[C@H]1O[C@@H](n2c3ncnc(N)c3nc2)[C@H](O)[C@@H]1O)OP(=O)(=O)O[C@@H]1[C@@H](CO)O[C@H](C1)n1ccc(=O)[nH]c1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5469,Fc1cnc(nc1Nc1nc2N(C(=O)C(Oc2cc1)(C)C)CO[P](=O)(=O)=O)Nc1cc(OC)c(OC)c(OC)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5474,n1(cc(c(=O)[nH]c1=O)C)[C@@H]1O[C@H](COP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]2O[C@H]([C@H](O)[C@@H]2O)n2c3ncnc(N)c3nc2)[C@H](C1)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5477,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5484,P(=O)(=O)(OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12)S,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5496,O=[P](=O)(=O)c1ccccc1OCC(=O)Nc1c(Cl)cccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5497,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2cnc3c(N)ncnc23)[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5511,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5514,O=[P](=O)(=O)C([P](=O)(=O)=O)Nc1cc(ccn1)c1ccc(cc1)OC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5516,P(=O)(=O)(OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O[P](=O)(=O)=O)n1c2ncnc(c2nc1)N)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5518,Clc1ccc(cc1Cl)[C@@H]([P](=O)(=O)=O)CCN(O)C(=O)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5537,[P](=O)(=O)(=O)O[C@@H]1[C@@H](CO[P](=O)(=O)=O)O[C@H]([C@@H]1O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5541,c1ccc(cc1)C([P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5554,c12ccccc1cccc2C[C@@H]1C/C=C/[C@@H](CC(=O)NC2(C(=O)N[C@H](C(=O)NC1)CC(=O)N)CCCCC2)c1ccc(cc1)C[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5556,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5568,C(O)C(=O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5570,C(=O)(CCC[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5582,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5596,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5597,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2[C@@H]([NH3+])N=CNc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](Cn1c2c(n(c3c(=O)[nH]c(=O)[nH]c13)C(=O)CCc1ccc(NC(=O)c3ccccc3)cc1)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5620,O=[P](=O)(=O)[C@@H]([NH3+])C(CCC)CCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5630,c1(c(Cl)cc(Cl)cc1)[C@H]([P](=O)(=O)=O)S,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5637,C([C@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O)O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5640,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5641,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5654,P(=O)(=O)(O[P](=O)(=O)=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1CN(C)c2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5655,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(=O)[nH]c(N)nc12)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5657,Cc1cc(=O)oc2c1ccc(O[P](=O)(=O)=O)c2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5663,O=[P](=O)(=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5691,c1(cc(F)cc(c1)F)S(=O)(=O)c1ccc(CNC(=O)C2CN3C=CN([C@H]4[C@H](O)[C@H](O)[C@@H](CO[P](=O)(=O)=O)O4)C3CC2)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5698,O=C[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5704,[P](=O)(=O)(=O)O[C@@H]1[C@@H]([C@@H](CO[P](=O)(=O)=O)O[C@H]1n1cnc2c(N)ncnc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5706,n1c(C)c(O)c(C[NH3+])c(c1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5728,O=c1[nH]c(=O)cc(C)n1[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5763,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5777,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5780,[NH3+][C@@H](CO[P](=O)(=O)=O)C(=O)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N)Cc2ccccc2)[C@H](O)C)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5789,[P](=O)(=O)(=O)OCC[N+](C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5791,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5805,O=[P](=O)(=O)O[C@@H]1[C@@H](O)[C@@H](O)CO[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5818,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5834,P(=O)(=O)(OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5865,[nH]1c(=O)[nH]c(=O)c2[nH]c3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5869,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5870,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5873,O=[P](=O)(=O)C(O)([P](=O)(=O)=O)Cc1cccnc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5881,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5889,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5891,O=c1ccn(c(=O)[nH]1)[C@H]1C[C@H](O)[C@H](O1)COP(=O)(=O)NP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5893,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5908,N([C@@H](C(C)C)C(=O)N[C@@H](CC(=O)N)C(=O)N[C@@H](CC(C)C)P(=O)(=O)O[C@@H](Cc1ccccc1)C(=O)OC)C(=O)CC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5911,P(=O)(=O)(OC[C@H]1O[C@@H](n2c3ncnc(N)c3nc2)[C@H](O)[C@@H]1O[P](=O)(=O)=O)OP(=O)(=O)O[C@@H]1[C@@H](CO[P](=O)(=O)=O)O[C@H](C1)n1ccc(=O)[nH]c1=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5915,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5925,COc1ccc(OC)c(c1)NC(=O)COc1ccccc1[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5931,c12c(c(=O)[nH]c(N)n1)ncn2[C@H]1CN(C(=O)C[P](=O)(=O)=O)C[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5940,O=[P](=O)(=O)[C@@H]([NH3+])C1CCCCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5941,c1(ccc(cc1)C(=O)NCCO[P](=O)(=O)=O)OC(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5964,O=[P](=O)(=O)COCC[N@@H+](CC[P](=O)(=O)=O)CCn1c2nc(N)[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5967,O=[P](=O)(=O)OP(=O)(=O)OCCc1c(C)n(nn1)Cc1cnc(C)nc1N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5988,O=[P](=O)(=O)C([P](=O)(=O)=O)C[NH2+]CCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5990,O=[P](=O)(=O)CCc1cnc(C)c(O)c1CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5992,[P](=O)(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,5994,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](n2cnc3c(N)ncnc23)[C@@H]([C@@H]1O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6006,O=c1ccn(c(=O)[nH]1)[C@H]1C[C@H](O)[C@H](O1)COP(=O)(=O)NP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6007,O=[P](=O)(=O)C([P](=O)(=O)=O)(O)Cn1cncc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6011,CCCCCCCCCC/C=C/[C@@H](O)[C@@H]([NH3+])COP(=O)(=O)OCC[N+](C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6013,[C@H]1([C@@H]([C@H]([C@H]([C@@H](CO)O1)O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6016,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6017,O=[P](=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(NCC=C(C)C)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6023,n1(c(=O)nc(cc1)N)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6031,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O[P](=O)(=O)=O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6034,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6038,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](O)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6043,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6045,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1CC(C(=O)N)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6054,C/N=C/1\CCCC=C1C(=O)O[C@H]1[C@@H](O)[C@H](n2cnc3c2ncnc3N)O[C@@H]1COP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6068,[P](=O)(=O)(=O)O[C@@H]1[C@@H](CO[P](=O)(=O)=O)O[C@H]([C@@H]1O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6073,O=[P](=O)(=O)CCOC[C@H](OCC[P](=O)(=O)=O)Cn1c2nc(N)[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6074,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6077,P(=O)(=O)(OP(=O)(=O)O[P](=O)(=O)=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c(=O)c(C)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6086,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6089,P(=O)(=O)(C)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6092,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6093,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6094,[P](=O)(=O)(=O)O[C@H]1[C@H](O[C@@]2(O[C@H]([C@H]([C@H](O)C2)C)C/C=C/c2nc(oc2)[C@H](CC)C)C1(C)C)[C@@H](OC)C[C@H](O)[C@@H]([C@H](O)[C@@H](/C=C(/C(=C/C=C/C(=C\C#N)/C)/C)\C)C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6095,[P](=O)(=O)(=O)OC[C@H](NC(=O)C)C[N@H+]1[C@@H](CCC1)C(=O)NCCc1c[nH]c2c1cccc2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6104,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6111,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6139,O=[P](=O)(=O)[C@@H]([NH3+])c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6163,c1cc(c(cc1C[C@@H](C(=O)N[C@H]1CCCCN(C1=O)Cc1ccc(cc1)c1ccccc1)NC(=O)C)[P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6164,c1ccc(cc1)[C@@](n1c2c(cccc2)nn1)(Cc1ccc(cc1)c1cc2ccc(C)nc2c(c1)[P](=O)(=O)=O)Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6178,O=[P](=O)(=O)OP(=O)(=O)CCC[C@@H](C)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6185,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6191,c12ccccc1oc1c2C=CC[C@@H]1c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6207,O(P(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)P(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1C=C(C(=O)N)CC=C1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6238,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)N1C=C(C(=O)N)C=CC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6241,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6245,c1(ccccc1)COc1c(cccc1F)[C@@H]1[C@]23[C@@H](N(C[C@@H]([C@@H]([C@@H](COP(=O)(=O)OP(=O)(=O)OC[C@@H]4[C@H]([C@H]([C@@H](O4)n4c5c(c(=N)nc[nH]5)nc4)O)O)O)O)O)c4c(cc(C)c(c4)C)N3[C@@H](C1)O)NC(=O)NC2=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6248,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6252,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6260,[P](=O)(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6283,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6295,C(=C\C=C\c1ccc(C([P](=O)(=O)=O)(F)F)cc1)/c1ccc(C([P](=O)(=O)=O)(F)F)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6320,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6326,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)O[C@@H]1[C@H](NC(=O)C)[C@@H](O)[C@H](O)[C@H](S1)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6339,C1CCCC1Nc1ncnc2c1ncn2[C@@H]1O[C@@H]([C@H]([C@H]1O)O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6343,C(CC([P](=O)(=O)=O)([P](=O)(=O)=O)O)[N@@H+](CCCCc1ccccc1)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6346,c1cc(cc2c1cccc2)S(=O)(=O)NCCO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6352,[C@H]1([C@@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)NP(=O)(=O)C)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6365,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1c(=O)[nH]c(=O)cc1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6368,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OC/C=C/CC[C@@H]1SC[C@@H]2NC(=O)N[C@H]12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6371,C(OP(=O)(=O)O[P](=O)(=O)=O)/C=C(/C)\CC/C=C(\C)/CCC=C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6373,OC(Cc1cnc2ccccn12)([P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6375,c12ccccc1oc1c2cccc1c1cc(ccc1)CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6385,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6389,[NH3+][C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C(=O)N1[C@H](C(=O)N[C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C(=O)N2[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H](CO[P](=O)(=O)=O)C=O)Cc3ccc(cc3)O)CO)CCC2)[C@H](O)C)CCC1)Cc1ccc(cc1)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6392,C(CC([P](=O)(=O)=O)([P](=O)(=O)=O)O)[N@@H+](CCCCc1ccccc1)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6412,O=[P](=O)(=O)[C@@H]([NH3+])[C@H](C)CCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6421,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6430,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6433,O=[P](=O)(=O)CC[N@@H+](CC=O)CC[N@H+](CC[P](=O)(=O)=O)CCn1c2nc(N)[nH]c(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6458,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6464,[P](=O)(=O)(=O)CP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6494,Nc1cccc(c1)COC(=O)N[C@@H](Cc1ccc(cc1)O[P](=O)(=O)=O)C(=O)NC1(CCCCC1)C(=O)N[C@@H]1C=CCC[C@@H]1C(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6496,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6514,OC[C@]1(O)[C@@H](O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6540,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6541,P(=O)(=O)(OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12)OC[C@H]1O[C@H]([C@H](O)[C@@H]1O)n1c2ncnc(N)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6547,n1cnc2c(c1N(CC)CC)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)C(Br)([P](=O)(=O)=O)Br,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6550,[P](=O)(=O)(=O)OC[C@]1(O)[C@@H](O)[C@H](O)[C@H](O1)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6567,[C@H]1([C@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6577,[P](=O)(=O)(=O)O[C@@H]1[C@@H](CO)O[C@H]([C@@H]1O)n1c(=O)[nH]c(=N)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6584,O=[P](=O)(=O)c1ccccc1OCC(=O)Nc1cc(Cl)cc(Cl)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6591,C(OCCCCCCCCCCCCCCCC)[C@@H](OP(=O)(=O)OC)COCC(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6605,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6623,[C@H]1(C[C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6624,CCCCCCCCCCOC1=CC(=CN(C1)CC([P](=O)(=O)=O)[P](=O)(=O)=O)c1cc(F)cc(F)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6626,P(=O)(=O)(OC/C=C(\C)/CC/C=C(/CCC=C(CF)CF)\C)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6637,P(=O)(=O)(S)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6655,OC1=CCN(C(=O)N1)[C@H]1C[C@H](O)[C@H](O1)COP(=O)(=O)NP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6678,[C@H]1([C@@H]([C@@H]([C@@H]([C@@H]([C@H]1O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6682,C(O[P](=O)(=O)=O)[C@]1(O)[C@@H](O)[C@H](O)[C@H](O1)CO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6706,[P](=O)(=O)(=O)OCC[N+](C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6710,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6712,c1(c2c([nH]c1)ccc(c2)F)CCCO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6725,C(C(=O)[C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6737,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6739,O=[P](=O)(=O)C([P](=O)(=O)=O)C[NH2+]CCCCCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6740,CCOP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6766,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]1O[C@@H]([C@@H]([C@@H]1O)O)N1C=C(C(=O)N)CC=C1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6782,C(C[NH3+])O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6784,n1c(=O)[nH]c(=O)c2nc3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6791,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6796,C1Cc2c(NC1)c(ccc2)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6804,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6822,O=[P](=O)(=O)[C@@H]([NH3+])c1ccc(cc1)n1nccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6825,C1[N+](C)(C)CCO[C@@]1(O)c1ccc(cc1)c1ccc(cc1)C(=O)C[N+](C)(C)CCO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6828,C(=O)(CCCCCNC(=O)CCCCC1CCCCC1)N1[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@H](C(=O)N[C@@H]([C@@H](C)O[P](=O)(=O)=O)C(=O)N[C@H](C(=O)N)CCSC)CO)Cc2nc[nH]c2)CC(C)C)CCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6832,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]cnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6834,O=[P](=O)(=O)C([P](=O)(=O)=O)C[NH2+]C1CCCCC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6846,C(C([P](=O)(=O)=O)(O)[P](=O)(=O)=O)N1CCCC(C1)c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6885,O=[P](=O)(=O)OP(=O)(=O)OCCC#C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6892,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6898,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)COP(=O)(=O)O[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6902,c1(ccc(cc1)OC)c1ccc(cc1)S(=O)(=O)N[C@@H](C(C)C)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6904,P(=O)(=O)(S)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6913,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6921,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6931,O=[P](=O)(=O)OP(=O)(=O)OC/C=C(\C)/CS,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6936,n1(cncc1N)[C@H]1[C@@H]([C@@H]([C@H](O1)CO[P](=O)(=O)=O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6938,c1(ccc(cc1)S(=O)(=O)NCCO[P](=O)(=O)=O)OC(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6942,[P](=O)(=O)(=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c(=O)c(CO)c1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6957,O=[P](=O)(=O)CNc1ncnc2sc(cc12)c1ccc(C)cc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6961,[C@H]1([C@@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)[NH3+])O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6976,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6985,[P](=O)(=O)(=O)O[C@@H]1[C@@H]([C@@H](CO)O[C@H]1n1cnc2c(=O)[nH]c(N)nc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6991,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]cnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,6998,CCCCCCCC/C=C/CCCCCCCC(=O)OC[C@H](COP(=O)(=O)OCC[N+](C)(C)C)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7004,C(=O)(CO[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7016,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7021,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7022,[P](=O)(=O)(=O)OCC[N+](C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7024,CCCCCCC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7042,n1(c(=O)[nH]c(=O)cc1)[C@H]1C[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7048,O=[P](=O)(=O)C([P](=O)(=O)=O)Nc1cc(ccn1)c1ccc(cc1)OC(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7055,O=P1(=O)O[C@@H]2[C@@H](CO1)O[C@H]([C@@H]2O)n1c2c(cc1)c(ncn2)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7059,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1[NH3+])O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7061,[P](=O)(=O)(=O)O[C@@H]1[C@@H]([C@@H](CO)O[C@H]1n1cnc2c(N)ncnc12)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7069,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7099,C(=O)(CO[P](=O)(=O)=O)NO,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7109,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](CN1c2c(N[C@@H]3C(=O)NC(=O)N[C@H]13)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7122,C(C(=O)[C@H]([C@@H](CO[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7127,NC(=O)[C@@H](NC(=O)[C@H](CCCc1ccccc1)CP(=O)(=O)[C@H](NC(=O)OCc1ccccc1)Cc1ccccc1)Cc1c2c([nH]c1)cccc2,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7137,[P](=O)(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7140,P(=O)(=O)(OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@@H](O1)n1c2ncnc(c2nc1)N)O)O)OC[C@H]([C@H]([C@H](CN1[C@@H]2NC(=O)NC(=O)[C@]32N(c2cc(c(cc12)C)C)[C@@]([NH3+])(C[C@H]3c1ccccc1)C)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7144,C(=O)(C)N[C@@H](CSCCNC(=O)[C@H]1N(C(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H](NC(=O)[C@@H]([NH3+])Cc2ccc(cc2)O[P](=O)(=O)=O)C(C)C)CC(=O)N)C(C)C)CCC1)C=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7149,[C@H]1([C@@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7153,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7159,CCCCCCCCCCOc1cncc(c1)CC([P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7161,P(=O)(=O)(OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)OP(=O)(=O)OC[C@H]([C@H]([C@H](Cn1c2c(n(c3c(nc(nc13)O)O)[C@H](CC=O)c1ccccc1)cc(C)c(C)c2)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7167,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7169,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](O)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7174,c1c(ccc(c1)CC(=O)N[C@H](C(=O)N[C@H](C(=O)N)Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O)Cc1ccccc1)C([P](=O)(=O)=O)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7181,[C@@](Cc1ccc(cc1)C(F)(F)[P](=O)(=O)=O)(n1nnc2c1cccc2)(c1sc2c(n1)cccc2)C/C=C/c1ccccc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7182,O=c1ccn(c(=O)[nH]1)[C@H]1C[C@H](O)[C@H](O1)COP(=O)(=O)NP(=O)(=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7186,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7193,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2OP(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7199,[C@@H]1([C@H]([C@H]([C@@H]([C@@H](CO[P](=O)(=O)=O)O1)O)O)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7222,[P](=O)(=O)(=O)NP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7223,[P](=O)(=O)(=O)OC[C@H]1O[C@@H](n2c3[nH]c(=O)[nH]c(=O)c3nc2)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7224,[P](=O)(=O)(=O)CC(=O)N,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7226,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7227,n1(c(=O)[nH]c(=O)cc1)[C@H]1[C@H](O)[C@@H]([C@H](O1)CO[P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7231,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O)O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7246,[P](=O)(=O)(=O)OC[C@H]1O[C@@H](n2c3[nH]c(=O)[nH]c(=O)c3nc2)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7260,[P](=O)(=O)(=O)OCC[N+](C)(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7261,CCCCCCCCCCCC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7265,O=C(COc1ccccc1[P](=O)(=O)=O)Nc1ccccc1C(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7277,C(C[NH3+])CC([P](=O)(=O)=O)([P](=O)(=O)=O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7282,c1cccc(c1)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7284,[nH]1c(=O)[nH]c(=O)c2[nH]c3cc(C)c(C)cc3n(c12)C[C@H](O)[C@H](O)[C@H](O)CO[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7303,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7304,[P](=O)(=O)(=O)[C@H](O)/C=C(\C)/CC/C=C(\C)/CCC=C(C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7312,O=[P](=O)(=O)OP(=O)(=O)OCC[NH+](C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7318,[C@H]1([C@@H]([C@@H]([C@H]([C@@H]([C@H]1O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O)O[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7324,O=[P](=O)(=O)[C@@H]([NH3+])C(CCC)CCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7331,[nH]1c(N)nc2c(c1=O)ncn2[C@H]1CCN(C1)C(=O)C[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7341,CCn1c2nc(nc(c2nc1)Nc1ccc(cc1)P(=O)(=O)C[P](=O)(=O)=O)[C@H]1CC[C@H]([NH3+])CC1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7348,P(=O)(=O)(OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12)C(F)(F)CCCC(F)(F)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7355,[C@@H]1(O[P](=O)(=O)=O)[C@H](O)[C@H](O[P](=O)(=O)=O)[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7356,O=[P](=O)(=O)OC[C@H]1O[C@H](C[C@@H]1O)n1c(=O)[nH]c2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7364,[C@H]1([C@@H]([C@H]([C@@H]([C@@H](CO)O1)O)O)NC(=O)C)OP(=O)(=O)OP(=O)(=O)OC[C@@H]1[C@H]([C@H]([C@H](n2c(=O)[nH]c(=O)cc2)O1)O)O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7366,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7371,O=[P](=O)(=O)O[C@H](C(=O)N[C@@H](CC(C)C)C(=O)Nc1ccc(cc1)N(=O)=O)CCCC,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7375,O=[P](=O)(=O)CC[NH+](CC[P](=O)(=O)=O)CC[N@@H+](CC[P](=O)(=O)=O)CCn1c2[nH]cnc(=O)c2nc1,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7391,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7424,n1c(c2c(cc1)cccc2)NC([P](=O)(=O)=O)[P](=O)(=O)=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7438,c1cc(cc(c1)S(=O)(=O)c1ccc(cc1)CNC(=O)c1cc2cnn(c2nc1)[C@H]1[C@@H]([C@@H]([C@H](O1)CO[P](=O)(=O)=O)O)O)C(F)(F)F,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7443,P1(=O)(=O)OC[C@H]2O[C@H]([C@@H]([C@@H]2O1)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7446,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7447,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(=O)[nH]c(N)nc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7448,n1cnc2c(c1N)ncn2[C@H]1[C@H](O)[C@H](O)[C@H](O1)COP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@@H](O)[C@H](O)[C@@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7461,CNc1ccccc1C(=O)O[C@H]1[C@@H](O)[C@@H](O[C@@H]1COP(=O)(=O)OP(=O)(=O)O[P](=O)(=O)=O)n1cnc2c1nc(N)[nH]c2=O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7462,[P](=O)(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@H]2[C@@H]1O[C@@]1(O2)C(=CC(=C[C@@H]1N(=O)=O)N(=O)=O)N(=O)=O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7467,[C@@H]1(O[P](=O)(=O)=O)[C@H](O)[C@H](O[P](=O)(=O)=O)[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7470,CC(=CCC/C(=C/CC/C(=C/CC/C(=C/COP(=O)(=O)O[P](=O)(=O)=O)/C)/C)/C)C,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7481,[C@@H]1(O[P](=O)(=O)=O)[C@H](O)[C@H](O[P](=O)(=O)=O)[C@@H](O[P](=O)(=O)=O)[C@H](O[P](=O)(=O)=O)[C@H]1O,RDKit MolFromSmiles returned None
2025-09-11T20:07:40,./data\pdb_train.csv,7505,[P](=O)(=O)(=O)OP(=O)(=O)OP(=O)(=O)OC[C@H]1O[C@H]([C@@H]([C@@H]1O)O)n1cnc2c(N)ncnc12,RDKit MolFromSmiles returned None
