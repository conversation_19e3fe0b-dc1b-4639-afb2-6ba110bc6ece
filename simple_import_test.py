#!/usr/bin/env python3
"""
简单的导入测试
"""
import sys
import os

print("开始导入测试...")

try:
    import torch
    print("✓ torch导入成功")
except Exception as e:
    print(f"✗ torch导入失败: {e}")
    sys.exit(1)

try:
    from models.core import LinearAttention, CrossAttention
    print("✓ 注意力类导入成功")
except Exception as e:
    print(f"✗ 注意力类导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

try:
    from models.core import PMMRNet
    print("✓ PMMRNet导入成功")
except Exception as e:
    print(f"✗ PMMRNet导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("所有导入测试通过！")
